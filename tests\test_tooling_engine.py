"""Tests for ToolingEngine."""

import pytest
import tempfile
from pathlib import Path

from core.tooling_engine import ToolingEngine


class TestToolingEngine:
    """Test cases for ToolingEngine."""
    
    @pytest.fixture
    def tooling_engine(self):
        """Create a ToolingEngine instance for testing."""
        config = {
            "shell": {"timeout": 30, "confirmation_required": False},
            "file_operations": {"max_file_size_mb": 1},
            "web_search": {"timeout": 10}
        }
        return ToolingEngine(config)
    
    def test_file_exists_tool(self, tooling_engine):
        """Test file_exists tool."""
        with tempfile.NamedTemporaryFile() as temp_file:
            # Test existing file
            result = tooling_engine.execute_tool("file_exists", {"path": temp_file.name})
            assert result.success
            assert result.result["exists"] is True
            assert result.result["is_file"] is True
        
        # Test non-existing file
        result = tooling_engine.execute_tool("file_exists", {"path": "/nonexistent/file.txt"})
        assert result.success
        assert result.result["exists"] is <PERSON>alse
    
    def test_read_write_file_tools(self, tooling_engine):
        """Test read_file and write_file tools."""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = Path(temp_dir) / "test.txt"
            test_content = "Hello, World!"
            
            # Test write_file
            result = tooling_engine.execute_tool("write_file", {
                "path": str(file_path),
                "content": test_content
            })
            assert result.success
            assert file_path.exists()
            
            # Test read_file
            result = tooling_engine.execute_tool("read_file", {"path": str(file_path)})
            assert result.success
            assert result.result == test_content
    
    def test_list_directory_tool(self, tooling_engine):
        """Test list_directory tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create some test files
            (Path(temp_dir) / "file1.txt").touch()
            (Path(temp_dir) / "file2.py").touch()
            (Path(temp_dir) / "subdir").mkdir()
            
            # Test directory listing
            result = tooling_engine.execute_tool("list_directory", {"path": temp_dir})
            assert result.success
            assert len(result.result) == 3
            
            # Check that all items are present
            names = [item["name"] for item in result.result]
            assert "file1.txt" in names
            assert "file2.py" in names
            assert "subdir" in names
    
    def test_create_directory_tool(self, tooling_engine):
        """Test create_directory tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            new_dir = Path(temp_dir) / "new_directory"
            
            # Test directory creation
            result = tooling_engine.execute_tool("create_directory", {"path": str(new_dir)})
            assert result.success
            assert new_dir.exists()
            assert new_dir.is_dir()
    
    def test_shell_command_tool(self, tooling_engine):
        """Test shell_command tool."""
        # Test simple command
        result = tooling_engine.execute_tool("shell_command", {"command": "echo 'test'"})
        assert result.success
        assert "test" in result.result["stdout"]
        assert result.result["returncode"] == 0
    
    def test_tool_schemas(self, tooling_engine):
        """Test that tool schemas are properly defined."""
        schemas = tooling_engine.get_tool_schemas()
        assert isinstance(schemas, list)
        assert len(schemas) > 0
        
        # Check that each schema has required fields
        for schema in schemas:
            assert "name" in schema
            assert "description" in schema
            assert "parameters" in schema
    
    def test_unknown_tool(self, tooling_engine):
        """Test execution of unknown tool."""
        result = tooling_engine.execute_tool("unknown_tool", {})
        assert not result.success
        assert "Unknown tool" in result.error
    
    def test_tool_error_handling(self, tooling_engine):
        """Test error handling in tool execution."""
        # Test reading non-existent file
        result = tooling_engine.execute_tool("read_file", {"path": "/nonexistent/file.txt"})
        assert not result.success
        assert result.error is not None
