"""Main CLI interface for the AI-powered terminal tool."""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.layout import Layout
from rich.live import Live
from prompt_toolkit import prompt
from prompt_toolkit.history import FileHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.shortcuts import confirm

from models.schemas import MessageRole, DiffRequest
from ui.animations import AnimationManager
from ui.diff_viewer import DiffViewer

logger = logging.getLogger(__name__)


class CLIInterface:
    """Main CLI interface for the AI terminal tool."""
    
    def __init__(self, config: Dict[str, Any], orchestrator):
        """Initialize CLI interface.
        
        Args:
            config: UI configuration
            orchestrator: Main orchestrator instance
        """
        self.config = config
        self.orchestrator = orchestrator
        
        # Initialize Rich console
        self.console = Console()
        
        # Initialize components
        self.animation_manager = AnimationManager(self.console, config)
        self.diff_viewer = DiffViewer(self.console, config)
        
        # Setup prompt history
        history_path = Path.home() / ".ai_cli_tool" / "history.txt"
        history_path.parent.mkdir(parents=True, exist_ok=True)
        self.history = FileHistory(str(history_path))
        
        # Command completions
        self.command_completer = WordCompleter([
            '/help', '/session', '/context', '/diff', '/history',
            '/clear', '/exit', '/quit', '/provider', '/status'
        ])
        
        # Status information
        self.current_session_id = None
        self.current_provider = None
        self.context_items_count = 0
    
    def start(self) -> None:
        """Start the CLI interface."""
        self._show_welcome()
        self._show_status_bar()
        
        try:
            while True:
                try:
                    # Get user input
                    user_input = self._get_user_input()
                    
                    if not user_input.strip():
                        continue
                    
                    # Handle internal commands
                    if user_input.startswith('/'):
                        if self._handle_internal_command(user_input):
                            continue
                        else:
                            break  # Exit command
                    
                    # Process with orchestrator
                    self._process_user_input(user_input)
                    
                except KeyboardInterrupt:
                    self.console.print("\n[yellow]Use /exit or /quit to exit[/yellow]")
                    continue
                except EOFError:
                    break
                    
        except Exception as e:
            logger.error(f"CLI error: {e}")
            self.console.print(f"[red]Unexpected error: {e}[/red]")
        
        finally:
            self._show_goodbye()
    
    def _show_welcome(self) -> None:
        """Display welcome message."""
        welcome_text = Text()
        welcome_text.append("🤖 AI-Powered CLI Terminal Tool\n", style="bold cyan")
        welcome_text.append("Type your commands or questions in natural language.\n", style="white")
        welcome_text.append("Use /help for available commands.\n", style="dim")
        
        panel = Panel(
            welcome_text,
            title="Welcome",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
        self.console.print()
    
    def _show_goodbye(self) -> None:
        """Display goodbye message."""
        self.console.print("\n[cyan]Goodbye! 👋[/cyan]")
    
    def _show_status_bar(self) -> None:
        """Display status bar with current information."""
        if not self.config.get("show_status_bar", True):
            return
        
        # Get status information from orchestrator
        status_info = self.orchestrator.get_status_info()
        
        status_text = Text()
        status_text.append("Session: ", style="dim")
        status_text.append(status_info.get("session_name", "default"), style="cyan")
        status_text.append(" | Provider: ", style="dim")
        status_text.append(status_info.get("provider", "none"), style="green")
        status_text.append(" | Context: ", style="dim")
        status_text.append(str(status_info.get("context_items", 0)), style="yellow")
        status_text.append(" items", style="dim")
        
        cwd = os.getcwd()
        if len(cwd) > 50:
            cwd = "..." + cwd[-47:]
        status_text.append(" | CWD: ", style="dim")
        status_text.append(cwd, style="blue")
        
        panel = Panel(
            status_text,
            border_style="dim",
            padding=(0, 1)
        )
        
        self.console.print(panel)
    
    def _get_user_input(self) -> str:
        """Get user input with prompt toolkit features.
        
        Returns:
            User input string
        """
        try:
            return prompt(
                "🤖 > ",
                history=self.history,
                auto_suggest=AutoSuggestFromHistory(),
                completer=self.command_completer,
                complete_style="column"
            )
        except (KeyboardInterrupt, EOFError):
            raise
    
    def _handle_internal_command(self, command: str) -> bool:
        """Handle internal commands.
        
        Args:
            command: Command string starting with /
            
        Returns:
            True to continue, False to exit
        """
        parts = command.strip().split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if cmd in ['/exit', '/quit']:
            return False
        
        elif cmd == '/help':
            self._show_help()
        
        elif cmd == '/clear':
            self.console.clear()
            self._show_status_bar()
        
        elif cmd == '/status':
            self._show_detailed_status()
        
        elif cmd == '/session':
            self._handle_session_command(args)
        
        elif cmd == '/context':
            self._handle_context_command(args)
        
        elif cmd == '/diff':
            self._handle_diff_command(args)
        
        elif cmd == '/history':
            self._show_history()
        
        elif cmd == '/provider':
            self._handle_provider_command(args)
        
        else:
            self.console.print(f"[red]Unknown command: {cmd}[/red]")
            self.console.print("Use /help for available commands.")
        
        return True
    
    def _show_help(self) -> None:
        """Display help information."""
        help_table = Table(title="Available Commands", show_header=True)
        help_table.add_column("Command", style="cyan", width=20)
        help_table.add_column("Description", style="white")
        
        commands = [
            ("/help", "Show this help message"),
            ("/status", "Show detailed status information"),
            ("/session list", "List all sessions"),
            ("/session new [name]", "Create new session"),
            ("/session switch <id>", "Switch to session"),
            ("/context list", "Show current context"),
            ("/context add <path>", "Add file/directory to context"),
            ("/context clear", "Clear explicit context"),
            ("/diff <file1> <file2>", "Show diff between files"),
            ("/diff git [file]", "Show git diff"),
            ("/history", "Show conversation history"),
            ("/provider list", "List available LLM providers"),
            ("/provider set <name>", "Set LLM provider"),
            ("/clear", "Clear screen"),
            ("/exit, /quit", "Exit the application")
        ]
        
        for cmd, desc in commands:
            help_table.add_row(cmd, desc)
        
        self.console.print(help_table)
    
    def _show_detailed_status(self) -> None:
        """Show detailed status information."""
        status_info = self.orchestrator.get_status_info()
        
        status_table = Table(title="System Status", show_header=True)
        status_table.add_column("Component", style="cyan", width=20)
        status_table.add_column("Status", style="white")
        
        # Add status rows
        for key, value in status_info.items():
            if isinstance(value, bool):
                value_str = "[green]✓[/green]" if value else "[red]✗[/red]"
            else:
                value_str = str(value)
            
            status_table.add_row(key.replace('_', ' ').title(), value_str)
        
        self.console.print(status_table)
    
    def _handle_session_command(self, args: List[str]) -> None:
        """Handle session-related commands."""
        if not args:
            self.console.print("[red]Session command requires arguments[/red]")
            self.console.print("Usage: /session [list|new|switch|delete] [args...]")
            return
        
        subcommand = args[0].lower()
        
        if subcommand == "list":
            sessions = self.orchestrator.list_sessions()
            if not sessions:
                self.console.print("[yellow]No sessions found[/yellow]")
                return
            
            sessions_table = Table(title="Sessions", show_header=True)
            sessions_table.add_column("ID", style="cyan")
            sessions_table.add_column("Name", style="white")
            sessions_table.add_column("Created", style="dim")
            sessions_table.add_column("Active", style="green")
            
            for session in sessions:
                active = "✓" if session.active else ""
                sessions_table.add_row(
                    session.session_id[:8],
                    session.name or "unnamed",
                    session.created_at.strftime("%Y-%m-%d %H:%M"),
                    active
                )
            
            self.console.print(sessions_table)
        
        elif subcommand == "new":
            name = args[1] if len(args) > 1 else None
            session = self.orchestrator.create_session(name)
            self.console.print(f"[green]Created new session: {session.name}[/green]")
        
        elif subcommand == "switch":
            if len(args) < 2:
                self.console.print("[red]Session ID required[/red]")
                return
            
            session_id = args[1]
            if self.orchestrator.switch_session(session_id):
                self.console.print(f"[green]Switched to session: {session_id}[/green]")
                self._show_status_bar()
            else:
                self.console.print(f"[red]Session not found: {session_id}[/red]")
        
        else:
            self.console.print(f"[red]Unknown session command: {subcommand}[/red]")
    
    def _handle_context_command(self, args: List[str]) -> None:
        """Handle context-related commands."""
        if not args:
            self.console.print("[red]Context command requires arguments[/red]")
            return
        
        subcommand = args[0].lower()
        
        if subcommand == "list":
            context_items = self.orchestrator.get_context_items()
            if not context_items:
                self.console.print("[yellow]No context items[/yellow]")
                return
            
            context_table = Table(title="Context Items", show_header=True)
            context_table.add_column("Type", style="cyan")
            context_table.add_column("Path", style="white")
            context_table.add_column("Content Preview", style="dim")
            
            for item in context_items:
                preview = item.content[:50] + "..." if item.content and len(item.content) > 50 else item.content or ""
                context_table.add_row(item.type, item.path or "", preview)
            
            self.console.print(context_table)
        
        elif subcommand == "add":
            if len(args) < 2:
                self.console.print("[red]Path required[/red]")
                return
            
            path = args[1]
            if self.orchestrator.add_context_item(path):
                self.console.print(f"[green]Added to context: {path}[/green]")
            else:
                self.console.print(f"[red]Failed to add: {path}[/red]")
        
        elif subcommand == "clear":
            self.orchestrator.clear_explicit_context()
            self.console.print("[green]Cleared explicit context[/green]")
        
        else:
            self.console.print(f"[red]Unknown context command: {subcommand}[/red]")
    
    def _handle_diff_command(self, args: List[str]) -> None:
        """Handle diff-related commands."""
        if not args:
            self.console.print("[red]Diff command requires arguments[/red]")
            return
        
        if args[0] == "git":
            file_path = args[1] if len(args) > 1 else None
            self.diff_viewer.show_git_diff(file_path)
        
        elif len(args) >= 2:
            file1, file2 = args[0], args[1]
            diff_type = args[2] if len(args) > 2 else "unified"
            self.diff_viewer.compare_files(file1, file2, diff_type)
        
        else:
            self.console.print("[red]Usage: /diff <file1> <file2> [type] or /diff git [file][/red]")
    
    def _show_history(self) -> None:
        """Show conversation history."""
        history = self.orchestrator.get_conversation_history()
        
        if not history:
            self.console.print("[yellow]No conversation history[/yellow]")
            return
        
        for message in history[-10:]:  # Show last 10 messages
            if message.role == MessageRole.USER:
                self.console.print(f"[cyan]User:[/cyan] {message.content}")
            elif message.role == MessageRole.ASSISTANT:
                self.console.print(f"[green]Assistant:[/green] {message.content}")
            self.console.print()
    
    def _handle_provider_command(self, args: List[str]) -> None:
        """Handle LLM provider commands."""
        if not args:
            self.console.print("[red]Provider command requires arguments[/red]")
            return
        
        subcommand = args[0].lower()
        
        if subcommand == "list":
            providers = self.orchestrator.get_available_providers()
            current = self.orchestrator.get_current_provider()
            
            provider_table = Table(title="LLM Providers", show_header=True)
            provider_table.add_column("Provider", style="cyan")
            provider_table.add_column("Status", style="white")
            provider_table.add_column("Current", style="green")
            
            for provider in providers:
                status = "[green]Available[/green]"
                current_mark = "✓" if provider == current else ""
                provider_table.add_row(provider, status, current_mark)
            
            self.console.print(provider_table)
        
        elif subcommand == "set":
            if len(args) < 2:
                self.console.print("[red]Provider name required[/red]")
                return
            
            provider = args[1]
            if self.orchestrator.set_provider(provider):
                self.console.print(f"[green]Switched to provider: {provider}[/green]")
                self._show_status_bar()
            else:
                self.console.print(f"[red]Provider not available: {provider}[/red]")
        
        else:
            self.console.print(f"[red]Unknown provider command: {subcommand}[/red]")
    
    def _process_user_input(self, user_input: str) -> None:
        """Process user input through the orchestrator.

        Args:
            user_input: User's input string
        """
        # Check if any providers are available
        available_providers = self.orchestrator.get_available_providers()
        if not available_providers:
            self.console.print("[red]No LLM providers are available![/red]")
            self.console.print("Please configure:")
            self.console.print("- Ollama (start local server)")
            self.console.print("- Deepseek (set API key)")
            return

        # Use streaming if available and enabled
        use_streaming = self.config.get("enable_streaming", True)

        # Check if current provider supports streaming
        current_provider = self.orchestrator.get_current_provider()
        if current_provider and use_streaming:
            try:
                self._process_user_input_stream(user_input)
            except Exception as e:
                logger.warning(f"Streaming failed, falling back to standard: {e}")
                self.console.print("[yellow]Streaming failed, using standard mode...[/yellow]")
                self._process_user_input_standard(user_input)
        else:
            self._process_user_input_standard(user_input)

    def _process_user_input_stream(self, user_input: str) -> None:
        """Process user input with streaming response.

        Args:
            user_input: User's input string
        """
        try:
            # Ensure any existing animations are stopped first
            self.animation_manager.stop_current_animation()

            # Give a moment for cleanup
            import time
            time.sleep(0.1)

            # Create a live display for streaming
            from rich.live import Live
            from rich.text import Text

            response_text = Text()
            response_text.append("🤖 ", style="cyan bold")

            with Live(response_text, console=self.console, refresh_per_second=10, transient=False) as live:
                full_response = ""
                chunk_count = 0

                for chunk in self.orchestrator.process_input_stream(user_input):
                    if chunk.strip():  # Only process non-empty chunks
                        full_response += chunk
                        response_text.append(chunk)
                        live.update(response_text)
                        chunk_count += 1

                        # Add a small delay to make streaming visible
                        if chunk_count % 5 == 0:
                            time.sleep(0.01)

            # Final newline for clean separation
            self.console.print()

        except Exception as e:
            # Ensure animation is stopped
            self.animation_manager.stop_current_animation()

            logger.error(f"Error streaming input: {e}")
            self.console.print(f"[red]Error: {e}[/red]")

            # Fallback to standard processing
            try:
                self.console.print("[yellow]Falling back to standard processing...[/yellow]")
                self._process_user_input_standard(user_input)
            except Exception as fallback_error:
                logger.error(f"Fallback processing also failed: {fallback_error}")
                self.console.print(f"[red]Processing failed: {fallback_error}[/red]")

        finally:
            # Update status bar
            self._show_status_bar()

    def _process_user_input_standard(self, user_input: str) -> None:
        """Process user input with standard response.

        Args:
            user_input: User's input string
        """
        # Start animation
        animation = self.animation_manager.start_ball_animation("Processing your request...")

        try:
            # Process through orchestrator
            response = self.orchestrator.process_input(user_input)

            # Stop animation
            self.animation_manager.stop_current_animation()

            # Display response
            self._display_response(response)

        except Exception as e:
            # Stop animation
            self.animation_manager.stop_current_animation()

            logger.error(f"Error processing input: {e}")
            self.console.print(f"[red]Error: {e}[/red]")

        finally:
            # Update status bar
            self._show_status_bar()
    
    def _display_response(self, response: Dict[str, Any]) -> None:
        """Display the response from orchestrator.
        
        Args:
            response: Response dictionary
        """
        if "error" in response:
            self.console.print(f"[red]Error: {response['error']}[/red]")
            return
        
        # Display main content
        content = response.get("content", "")
        if content:
            # Try to render as markdown if it looks like markdown
            if any(marker in content for marker in ['#', '*', '`', '```']):
                try:
                    markdown = Markdown(content)
                    self.console.print(markdown)
                except:
                    self.console.print(content)
            else:
                self.console.print(content)
        
        # Display tool results if any
        tool_results = response.get("tool_results", [])
        for result in tool_results:
            self._display_tool_result(result)
        
        # Display any diffs
        if "diff" in response:
            diff_request = DiffRequest(**response["diff"])
            self.diff_viewer.show_diff(diff_request)
    
    def _display_tool_result(self, result: Dict[str, Any]) -> None:
        """Display a tool execution result.
        
        Args:
            result: Tool result dictionary
        """
        tool_name = result.get("tool_name", "unknown")
        success = result.get("success", False)
        
        if success:
            self.console.print(f"[green]✓ {tool_name}[/green]")
            
            # Display result based on tool type
            tool_result = result.get("result", {})
            
            if tool_name == "shell_command":
                if tool_result.get("stdout"):
                    syntax = Syntax(tool_result["stdout"], "bash", theme="monokai")
                    self.console.print(syntax)
                if tool_result.get("stderr"):
                    self.console.print(f"[yellow]stderr: {tool_result['stderr']}[/yellow]")
            
            elif tool_name in ["read_file", "write_file"]:
                if isinstance(tool_result, str):
                    # File content
                    if len(tool_result) > 1000:
                        self.console.print(f"[dim]File content ({len(tool_result)} chars):[/dim]")
                        self.console.print(tool_result[:500] + "\n...\n" + tool_result[-500:])
                    else:
                        self.console.print(tool_result)
            
            elif tool_name == "web_search":
                if isinstance(tool_result, list):
                    search_table = Table(title="Search Results", show_header=True)
                    search_table.add_column("Title", style="cyan")
                    search_table.add_column("URL", style="blue")
                    
                    for item in tool_result[:5]:  # Show top 5 results
                        search_table.add_row(
                            item.get("title", "")[:50],
                            item.get("url", "")[:60]
                        )
                    
                    self.console.print(search_table)
        
        else:
            error = result.get("error", "Unknown error")
            self.console.print(f"[red]✗ {tool_name}: {error}[/red]")
