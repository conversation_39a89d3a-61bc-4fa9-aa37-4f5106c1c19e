"""AI Core for LLM integration and prompt engineering."""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
import requests
import httpx
from datetime import datetime

from models.schemas import (
    LLMRequest, LLMResponse, LLMProvider, ToolCall, 
    Message, ContextItem
)

logger = logging.getLogger(__name__)


class LLMAdapter(ABC):
    """Abstract base class for LLM adapters."""
    
    @abstractmethod
    def generate_response(self, request: LLMRequest) -> LLMResponse:
        """Generate response from LLM."""
        pass
    
    @abstractmethod
    def stream_response(self, request: LLMRequest):
        """Stream response from LLM."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if LLM is available."""
        pass


class OllamaAdapter(LLMAdapter):
    """Adapter for Ollama local LLM."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Ollama adapter.
        
        Args:
            config: Ollama configuration
        """
        self.server_url = config.get("server_url", "http://localhost:11434")
        self.default_model = config.get("default_model", "llama2")
        self.timeout = config.get("timeout", 30)
    
    def generate_response(self, request: LLMRequest) -> LLMResponse:
        """Generate response from Ollama."""
        try:
            # Prepare messages for Ollama format
            messages = []
            
            if request.system_prompt:
                messages.append({
                    "role": "system",
                    "content": request.system_prompt
                })
            
            # Add context if provided
            if request.context:
                context_content = self._format_context(request.context)
                messages.append({
                    "role": "system", 
                    "content": f"Context:\n{context_content}"
                })
            
            # Add conversation history
            if request.history:
                for msg in request.history[-10:]:  # Limit history
                    messages.append({
                        "role": msg.role.value,
                        "content": msg.content
                    })
            
            # Add current prompt
            messages.append({
                "role": "user",
                "content": request.prompt
            })
            
            # Add tools schema if provided
            tools_info = ""
            if request.tools_schema:
                tools_info = f"\n\nAvailable tools:\n{json.dumps(request.tools_schema, indent=2)}"
                tools_info += "\n\nTo use a tool, respond with JSON: {\"tool_name\": \"name\", \"arguments\": {...}}"
                messages[-1]["content"] += tools_info
            
            # Make request to Ollama
            payload = {
                "model": self.default_model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": request.temperature,
                }
            }
            
            if request.max_tokens:
                payload["options"]["num_predict"] = request.max_tokens
            
            response = requests.post(
                f"{self.server_url}/api/chat",
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            content = result.get("message", {}).get("content", "")
            
            # Try to parse tool calls
            tool_calls = self._parse_tool_calls(content)
            
            return LLMResponse(
                content=content,
                tool_calls=tool_calls,
                model=self.default_model,
                usage=result.get("usage", {}),
                finish_reason=result.get("done_reason")
            )
            
        except Exception as e:
            logger.error(f"Ollama request failed: {e}")
            raise RuntimeError(f"Ollama request failed: {e}")
    
    def stream_response(self, request: LLMRequest):
        """Stream response from Ollama."""
        # Implementation for streaming would go here
        # For now, just return the full response
        response = self.generate_response(request)
        yield response.content
    
    def is_available(self) -> bool:
        """Check if Ollama is available."""
        try:
            response = requests.get(f"{self.server_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _format_context(self, context: List[ContextItem]) -> str:
        """Format context items for prompt."""
        context_parts = []
        for item in context:
            if item.type == "directory":
                context_parts.append(f"Current directory: {item.path}")
            elif item.type == "file_listing":
                context_parts.append(f"Files: {item.content}")
            elif item.type == "git_status":
                context_parts.append(f"Git: {item.content}")
            elif item.type == "file" and item.content:
                context_parts.append(f"File {item.path}:\n{item.content[:1000]}...")
        
        return "\n".join(context_parts)
    
    def _parse_tool_calls(self, content: str) -> Optional[List[ToolCall]]:
        """Parse tool calls from LLM response."""
        try:
            # Look for JSON in the response
            content = content.strip()
            if content.startswith('{') and content.endswith('}'):
                data = json.loads(content)
                if "tool_name" in data and "arguments" in data:
                    return [ToolCall(
                        tool_name=data["tool_name"],
                        arguments=data["arguments"]
                    )]
            return None
        except:
            return None


class DeepseekAdapter(LLMAdapter):
    """Adapter for Deepseek API."""
    
    def __init__(self, config: Dict[str, Any], api_key: str):
        """Initialize Deepseek adapter.

        Args:
            config: Deepseek configuration
            api_key: API key
        """
        self.api_key = api_key
        # Ensure base_url doesn't have trailing /v1 if it's already included
        base_url = config.get("base_url", "https://api.deepseek.com")
        if base_url.endswith("/v1"):
            self.base_url = base_url
        else:
            self.base_url = f"{base_url}/v1" if not base_url.endswith("/") else f"{base_url}v1"
        self.model = config.get("model", "deepseek-chat")
        self.timeout = config.get("timeout", 30)

        logger.info(f"Deepseek adapter initialized with base_url: {self.base_url}, model: {self.model}")
    
    def generate_response(self, request: LLMRequest) -> LLMResponse:
        """Generate response from Deepseek."""
        try:
            # Prepare messages
            messages = []
            
            if request.system_prompt:
                messages.append({
                    "role": "system",
                    "content": request.system_prompt
                })
            
            # Add context
            if request.context:
                context_content = self._format_context(request.context)
                messages.append({
                    "role": "system",
                    "content": f"Context:\n{context_content}"
                })
            
            # Add history
            if request.history:
                for msg in request.history[-10:]:
                    messages.append({
                        "role": msg.role.value,
                        "content": msg.content
                    })
            
            # Add current prompt
            messages.append({
                "role": "user",
                "content": request.prompt
            })
            
            # Prepare payload
            payload = {
                "model": self.model,
                "messages": messages,
                "temperature": request.temperature,
                "stream": False
            }
            
            if request.max_tokens:
                payload["max_tokens"] = request.max_tokens
            
            # Add tools if provided
            if request.tools_schema:
                payload["tools"] = [
                    {"type": "function", "function": tool}
                    for tool in request.tools_schema
                ]
                payload["tool_choice"] = "auto"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Use the correct endpoint without double /v1
            chat_url = f"{self.base_url}/chat/completions"
            logger.debug(f"Making Deepseek API request to: {chat_url}")

            response = requests.post(
                chat_url,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            result = response.json()
            choice = result["choices"][0]
            message = choice["message"]
            
            content = message.get("content", "")
            tool_calls = None
            
            # Parse tool calls if present
            if "tool_calls" in message:
                tool_calls = []
                for tc in message["tool_calls"]:
                    if tc["type"] == "function":
                        func = tc["function"]
                        tool_calls.append(ToolCall(
                            tool_name=func["name"],
                            arguments=json.loads(func["arguments"]),
                            call_id=tc.get("id")
                        ))
            
            return LLMResponse(
                content=content,
                tool_calls=tool_calls,
                model=self.model,
                usage=result.get("usage", {}),
                finish_reason=choice.get("finish_reason")
            )
            
        except Exception as e:
            logger.error(f"Deepseek request failed: {e}")
            raise RuntimeError(f"Deepseek request failed: {e}")
    
    def stream_response(self, request: LLMRequest):
        """Stream response from Deepseek."""
        try:
            # Prepare the same payload as generate_response but with streaming
            messages = []

            # Add system message if provided
            if request.system_prompt:
                messages.append({
                    "role": "system",
                    "content": request.system_prompt
                })

            # Add conversation history
            if request.history:
                for msg in request.history:
                    messages.append({
                        "role": msg.role.value,
                        "content": msg.content
                    })

            # Add context if provided
            context_str = ""
            if request.context:
                context_str = self._format_context(request.context)
                if context_str:
                    context_str = f"\n\nContext:\n{context_str}"

            # Add user message with context
            user_content = request.prompt + context_str
            messages.append({
                "role": "user",
                "content": user_content
            })

            payload = {
                "model": self.model,
                "messages": messages,
                "stream": True,
                "temperature": 0.7,
                "max_tokens": 4000
            }

            # Add tools if provided
            if request.tools_schema:
                payload["tools"] = request.tools_schema
                payload["tool_choice"] = "auto"

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            chat_url = f"{self.base_url}/chat/completions"

            with requests.post(
                chat_url,
                json=payload,
                headers=headers,
                timeout=self.timeout,
                stream=True
            ) as response:
                response.raise_for_status()

                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data = line[6:]
                            if data == '[DONE]':
                                break
                            try:
                                chunk = json.loads(data)
                                if 'choices' in chunk and chunk['choices']:
                                    delta = chunk['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield delta['content']
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            logger.error(f"Deepseek streaming failed: {e}")
            # Fallback to non-streaming
            response = self.generate_response(request)
            yield response.content
    
    def is_available(self) -> bool:
        """Check if Deepseek API is available."""
        if not self.api_key or not self.api_key.strip():
            logger.debug("Deepseek API key not configured")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            # Use the correct endpoint without double /v1
            models_url = f"{self.base_url}/models"
            logger.debug(f"Checking Deepseek availability at: {models_url}")

            response = requests.get(
                models_url,
                headers=headers,
                timeout=10  # Increased timeout for better reliability
            )

            is_available = response.status_code == 200
            if is_available:
                logger.info("Deepseek API is available")
            else:
                logger.warning(f"Deepseek API check failed with status: {response.status_code}")
                logger.debug(f"Response: {response.text}")

            return is_available

        except requests.exceptions.Timeout:
            logger.warning("Deepseek API availability check timed out")
            return False
        except requests.exceptions.ConnectionError:
            logger.warning("Deepseek API connection failed")
            return False
        except Exception as e:
            logger.warning(f"Deepseek API availability check failed: {e}")
            return False
    
    def _format_context(self, context: List[ContextItem]) -> str:
        """Format context items for prompt."""
        context_parts = []
        for item in context:
            if item.type == "directory":
                context_parts.append(f"Current directory: {item.path}")
            elif item.type == "file_listing":
                context_parts.append(f"Files: {item.content}")
            elif item.type == "git_status":
                context_parts.append(f"Git: {item.content}")
            elif item.type == "file" and item.content:
                context_parts.append(f"File {item.path}:\n{item.content[:1000]}...")
        
        return "\n".join(context_parts)


class AICore:
    """Core AI component for LLM integration and prompt engineering."""
    
    def __init__(self, config: Dict[str, Any], config_manager):
        """Initialize AI Core.
        
        Args:
            config: LLM configuration
            config_manager: Configuration manager instance
        """
        self.config = config
        self.config_manager = config_manager
        self.adapters: Dict[str, LLMAdapter] = {}
        self.current_provider = config.get("default_provider", "ollama")
        
        # Initialize adapters
        self._initialize_adapters()
    
    def _initialize_adapters(self):
        """Initialize LLM adapters."""
        logger.info("Initializing LLM adapters...")

        # Initialize Ollama adapter
        if "ollama" in self.config:
            try:
                self.adapters["ollama"] = OllamaAdapter(self.config["ollama"])
                logger.info("Ollama adapter initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Ollama adapter: {e}")

        # Initialize Deepseek adapter
        if "deepseek" in self.config:
            try:
                api_key = self.config_manager.get_api_key("deepseek")
                if api_key:
                    self.adapters["deepseek"] = DeepseekAdapter(
                        self.config["deepseek"],
                        api_key
                    )
                    logger.info("Deepseek adapter initialized")
                else:
                    logger.warning("Deepseek configuration found but no API key available")
            except Exception as e:
                logger.error(f"Failed to initialize Deepseek adapter: {e}")

        logger.info(f"Initialized {len(self.adapters)} LLM adapters: {list(self.adapters.keys())}")
    
    def get_available_providers(self) -> List[str]:
        """Get list of available LLM providers.
        
        Returns:
            List of provider names
        """
        available = []
        for name, adapter in self.adapters.items():
            if adapter.is_available():
                available.append(name)
        return available
    
    def set_provider(self, provider: str) -> bool:
        """Set current LLM provider.
        
        Args:
            provider: Provider name
            
        Returns:
            True if successful
        """
        if provider in self.adapters and self.adapters[provider].is_available():
            self.current_provider = provider
            logger.info(f"Switched to LLM provider: {provider}")
            return True
        return False
    
    def generate_response(self, prompt: str, context: Optional[List[ContextItem]] = None,
                         history: Optional[List[Message]] = None,
                         tools_schema: Optional[List[Dict[str, Any]]] = None,
                         **kwargs) -> LLMResponse:
        """Generate response from current LLM.
        
        Args:
            prompt: User prompt
            context: Context items
            history: Conversation history
            tools_schema: Available tools schema
            **kwargs: Additional parameters
            
        Returns:
            LLM response
        """
        if self.current_provider not in self.adapters:
            raise RuntimeError(f"No adapter available for provider: {self.current_provider}")
        
        adapter = self.adapters[self.current_provider]
        
        # Create system prompt
        system_prompt = self._create_system_prompt(tools_schema is not None)
        
        request = LLMRequest(
            prompt=prompt,
            system_prompt=system_prompt,
            context=context,
            history=history,
            tools_schema=tools_schema,
            **kwargs
        )
        
        return adapter.generate_response(request)
    
    def _create_system_prompt(self, has_tools: bool = False) -> str:
        """Create system prompt for LLM.
        
        Args:
            has_tools: Whether tools are available
            
        Returns:
            System prompt
        """
        prompt_parts = [
            "You are an expert CLI assistant designed to help users accomplish tasks on their system.",
            "You have access to the user's current environment and can execute various tools to help them.",
            "Be concise but thorough in your responses.",
            "If a command or action could be risky, warn the user clearly.",
            "For complex requests, break them down into steps and execute them systematically."
        ]
        
        if has_tools:
            prompt_parts.extend([
                "",
                "You have access to various tools for:",
                "- Executing shell commands",
                "- Reading and writing files", 
                "- Searching the web",
                "- Managing directories",
                "",
                "When you need to use a tool, respond with a JSON object containing 'tool_name' and 'arguments'.",
                "You can chain multiple tool calls to accomplish complex tasks.",
                "Always consider the user's safety and ask for confirmation for destructive operations."
            ])
        
        return "\n".join(prompt_parts)
