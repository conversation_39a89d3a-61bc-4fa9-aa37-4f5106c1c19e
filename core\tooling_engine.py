"""Tooling engine for executing various tools and functions."""

import os
import subprocess
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import time
import logging
import requests
from bs4 import BeautifulSoup
import trafilatura

from models.schemas import (
    ToolResult, FileOperation, ShellCommand, 
    WebSearchQuery, ToolType
)

logger = logging.getLogger(__name__)


class ToolingEngine:
    """Executes various tools and functions for the AI assistant."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize tooling engine.
        
        Args:
            config: Tool configuration settings
        """
        self.config = config
        self.shell_config = config.get("shell", {})
        self.file_config = config.get("file_operations", {})
        self.web_config = config.get("web_search", {})
        
        # Tool registry
        self.tools = {
            "shell_command": self.execute_shell_command,
            "read_file": self.read_file,
            "write_file": self.write_file,
            "list_directory": self.list_directory,
            "create_directory": self.create_directory,
            "delete_file": self.delete_file,
            "delete_directory": self.delete_directory,
            "move_file": self.move_file,
            "copy_file": self.copy_file,
            "file_exists": self.file_exists,
            "web_search": self.web_search,
            "fetch_webpage": self.fetch_webpage,
            "git_status": self.git_status,
            "git_add": self.git_add,
            "git_commit": self.git_commit,
            "git_push": self.git_push,
            "git_pull": self.git_pull,
            "git_branch": self.git_branch,
            "find_files": self.find_files,
            "search_in_files": self.search_in_files,
            "replace_in_files": self.replace_in_files
        }
    
    def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolResult:
        """Execute a tool with given arguments.
        
        Args:
            tool_name: Name of tool to execute
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        start_time = time.time()
        
        try:
            if tool_name not in self.tools:
                return ToolResult(
                    tool_name=tool_name,
                    success=False,
                    result=None,
                    error=f"Unknown tool: {tool_name}",
                    execution_time=time.time() - start_time
                )
            
            tool_func = self.tools[tool_name]
            result = tool_func(**arguments)
            
            return ToolResult(
                tool_name=tool_name,
                success=True,
                result=result,
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            return ToolResult(
                tool_name=tool_name,
                success=False,
                result=None,
                error=str(e),
                execution_time=time.time() - start_time
            )
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get schemas for all available tools.
        
        Returns:
            List of tool schemas for LLM function calling
        """
        return [
            {
                "name": "shell_command",
                "description": "Execute a shell command",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {"type": "string", "description": "Command to execute"},
                        "cwd": {"type": "string", "description": "Working directory (optional)"},
                        "timeout": {"type": "integer", "description": "Timeout in seconds (optional)"}
                    },
                    "required": ["command"]
                }
            },
            {
                "name": "read_file",
                "description": "Read content of a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to read"},
                        "encoding": {"type": "string", "description": "File encoding (default: utf-8)"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "write_file",
                "description": "Write content to a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to write"},
                        "content": {"type": "string", "description": "Content to write"},
                        "encoding": {"type": "string", "description": "File encoding (default: utf-8)"},
                        "append": {"type": "boolean", "description": "Append to file (default: false)"}
                    },
                    "required": ["path", "content"]
                }
            },
            {
                "name": "list_directory",
                "description": "List contents of a directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path to list"},
                        "pattern": {"type": "string", "description": "File pattern filter (optional)"},
                        "recursive": {"type": "boolean", "description": "Recursive listing (default: false)"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "create_directory",
                "description": "Create a directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path to create"},
                        "parents": {"type": "boolean", "description": "Create parent directories (default: true)"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "delete_file",
                "description": "Delete a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to delete"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "file_exists",
                "description": "Check if a file or directory exists",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to check"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "web_search",
                "description": "Search the web for information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "num_results": {"type": "integer", "description": "Number of results (default: 5)"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "git_status",
                "description": "Get git repository status",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Repository path (optional)"}
                    },
                    "required": []
                }
            },
            {
                "name": "git_add",
                "description": "Add files to git staging area",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "files": {"type": "string", "description": "Files to add (can be . for all)"},
                        "path": {"type": "string", "description": "Repository path (optional)"}
                    },
                    "required": ["files"]
                }
            },
            {
                "name": "git_commit",
                "description": "Commit staged changes",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "description": "Commit message"},
                        "path": {"type": "string", "description": "Repository path (optional)"}
                    },
                    "required": ["message"]
                }
            },
            {
                "name": "find_files",
                "description": "Find files matching pattern",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pattern": {"type": "string", "description": "File pattern to search for"},
                        "path": {"type": "string", "description": "Directory to search in (default: current)"},
                        "recursive": {"type": "boolean", "description": "Search recursively (default: true)"}
                    },
                    "required": ["pattern"]
                }
            },
            {
                "name": "search_in_files",
                "description": "Search for text within files",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pattern": {"type": "string", "description": "Text pattern to search for"},
                        "file_pattern": {"type": "string", "description": "File pattern to search in (default: *)"},
                        "path": {"type": "string", "description": "Directory to search in (default: current)"},
                        "case_sensitive": {"type": "boolean", "description": "Case sensitive search (default: false)"}
                    },
                    "required": ["pattern"]
                }
            }
        ]
    
    # Shell Command Execution
    def execute_shell_command(self, command: str, cwd: Optional[str] = None, 
                            timeout: Optional[int] = None) -> Dict[str, Any]:
        """Execute a shell command.
        
        Args:
            command: Command to execute
            cwd: Working directory
            timeout: Timeout in seconds
            
        Returns:
            Command execution result
        """
        if timeout is None:
            timeout = self.shell_config.get("timeout", 60)
        
        # Security check for restricted commands
        restricted_commands = self.config.get("security", {}).get("restricted_commands", [])
        for restricted in restricted_commands:
            if restricted.lower() in command.lower():
                raise ValueError(f"Command contains restricted pattern: {restricted}")
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            return {
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
                "command": command,
                "success": result.returncode == 0
            }
            
        except subprocess.TimeoutExpired:
            raise TimeoutError(f"Command timed out after {timeout} seconds")
        except Exception as e:
            raise RuntimeError(f"Command execution failed: {e}")
    
    # File Operations
    def read_file(self, path: str, encoding: str = "utf-8") -> str:
        """Read file content.
        
        Args:
            path: File path
            encoding: File encoding
            
        Returns:
            File content
        """
        file_path = Path(path).resolve()
        
        # Check file size
        max_size_mb = self.file_config.get("max_file_size_mb", 10)
        if file_path.stat().st_size > max_size_mb * 1024 * 1024:
            raise ValueError(f"File too large (max {max_size_mb}MB)")
        
        return file_path.read_text(encoding=encoding)
    
    def write_file(self, path: str, content: str, encoding: str = "utf-8", 
                   append: bool = False) -> Dict[str, Any]:
        """Write content to file.
        
        Args:
            path: File path
            content: Content to write
            encoding: File encoding
            append: Whether to append
            
        Returns:
            Write operation result
        """
        file_path = Path(path).resolve()
        
        # Create parent directories if needed
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        mode = "a" if append else "w"
        file_path.write_text(content, encoding=encoding)
        
        return {
            "path": str(file_path),
            "bytes_written": len(content.encode(encoding)),
            "mode": mode
        }
    
    def list_directory(self, path: str, pattern: Optional[str] = None, 
                      recursive: bool = False) -> List[Dict[str, Any]]:
        """List directory contents.
        
        Args:
            path: Directory path
            pattern: File pattern filter
            recursive: Whether to list recursively
            
        Returns:
            List of directory items
        """
        dir_path = Path(path).resolve()
        
        if not dir_path.is_dir():
            raise ValueError(f"Path is not a directory: {path}")
        
        items = []
        
        if recursive:
            glob_pattern = "**/*" if not pattern else f"**/{pattern}"
            paths = dir_path.glob(glob_pattern)
        else:
            glob_pattern = "*" if not pattern else pattern
            paths = dir_path.glob(glob_pattern)
        
        for item_path in sorted(paths):
            try:
                stat = item_path.stat()
                items.append({
                    "name": item_path.name,
                    "path": str(item_path),
                    "type": "directory" if item_path.is_dir() else "file",
                    "size": stat.st_size,
                    "modified": stat.st_mtime,
                    "permissions": oct(stat.st_mode)[-3:]
                })
            except Exception as e:
                logger.debug(f"Error getting stats for {item_path}: {e}")
        
        return items
    
    def create_directory(self, path: str, parents: bool = True) -> Dict[str, Any]:
        """Create directory.
        
        Args:
            path: Directory path
            parents: Create parent directories
            
        Returns:
            Creation result
        """
        dir_path = Path(path).resolve()
        dir_path.mkdir(parents=parents, exist_ok=True)
        
        return {
            "path": str(dir_path),
            "created": True,
            "exists": dir_path.exists()
        }
    
    def delete_file(self, path: str) -> Dict[str, Any]:
        """Delete a file.
        
        Args:
            path: File path
            
        Returns:
            Deletion result
        """
        file_path = Path(path).resolve()
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        if file_path.is_dir():
            raise ValueError(f"Path is a directory, not a file: {path}")
        
        file_path.unlink()
        
        return {
            "path": str(file_path),
            "deleted": True
        }
    
    def delete_directory(self, path: str, recursive: bool = False) -> Dict[str, Any]:
        """Delete a directory.
        
        Args:
            path: Directory path
            recursive: Delete recursively
            
        Returns:
            Deletion result
        """
        dir_path = Path(path).resolve()
        
        if not dir_path.exists():
            raise FileNotFoundError(f"Directory not found: {path}")
        
        if not dir_path.is_dir():
            raise ValueError(f"Path is not a directory: {path}")
        
        if recursive:
            shutil.rmtree(dir_path)
        else:
            dir_path.rmdir()  # Only works if empty
        
        return {
            "path": str(dir_path),
            "deleted": True
        }
    
    def move_file(self, source: str, destination: str) -> Dict[str, Any]:
        """Move/rename a file or directory.
        
        Args:
            source: Source path
            destination: Destination path
            
        Returns:
            Move operation result
        """
        source_path = Path(source).resolve()
        dest_path = Path(destination).resolve()
        
        if not source_path.exists():
            raise FileNotFoundError(f"Source not found: {source}")
        
        # Create destination parent directory if needed
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.move(str(source_path), str(dest_path))
        
        return {
            "source": str(source_path),
            "destination": str(dest_path),
            "moved": True
        }
    
    def copy_file(self, source: str, destination: str) -> Dict[str, Any]:
        """Copy a file or directory.
        
        Args:
            source: Source path
            destination: Destination path
            
        Returns:
            Copy operation result
        """
        source_path = Path(source).resolve()
        dest_path = Path(destination).resolve()
        
        if not source_path.exists():
            raise FileNotFoundError(f"Source not found: {source}")
        
        # Create destination parent directory if needed
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        if source_path.is_file():
            shutil.copy2(str(source_path), str(dest_path))
        else:
            shutil.copytree(str(source_path), str(dest_path))
        
        return {
            "source": str(source_path),
            "destination": str(dest_path),
            "copied": True
        }
    
    def file_exists(self, path: str) -> Dict[str, Any]:
        """Check if file or directory exists.
        
        Args:
            path: Path to check
            
        Returns:
            Existence check result
        """
        file_path = Path(path).resolve()
        
        return {
            "path": str(file_path),
            "exists": file_path.exists(),
            "is_file": file_path.is_file() if file_path.exists() else None,
            "is_directory": file_path.is_dir() if file_path.exists() else None
        }
    
    # Web Operations
    def web_search(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search the web (simplified implementation).
        
        Args:
            query: Search query
            num_results: Number of results
            
        Returns:
            Search results
        """
        # This is a simplified implementation
        # In production, you'd use a proper search API
        try:
            # Use DuckDuckGo as a simple search option
            search_url = f"https://duckduckgo.com/html/?q={query}"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            timeout = self.web_config.get("timeout", 15)
            response = requests.get(search_url, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Parse search results (simplified)
            for result in soup.find_all('a', class_='result__a')[:num_results]:
                title = result.get_text(strip=True)
                url = result.get('href', '')
                
                if title and url:
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": ""  # Would need more parsing for snippets
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return [{"error": f"Search failed: {e}"}]
    
    def fetch_webpage(self, url: str) -> Dict[str, Any]:
        """Fetch and extract content from a webpage.
        
        Args:
            url: URL to fetch
            
        Returns:
            Webpage content
        """
        try:
            timeout = self.web_config.get("timeout", 15)
            
            # Use trafilatura for content extraction
            content = trafilatura.fetch_url(url, timeout=timeout)
            if content:
                extracted = trafilatura.extract(content)
                return {
                    "url": url,
                    "content": extracted,
                    "success": True
                }
            else:
                return {
                    "url": url,
                    "content": None,
                    "success": False,
                    "error": "Failed to fetch content"
                }
                
        except Exception as e:
            logger.error(f"Webpage fetch error: {e}")
            return {
                "url": url,
                "content": None,
                "success": False,
                "error": str(e)
            }

    # Git Operations
    def git_status(self, path: Optional[str] = None) -> Dict[str, Any]:
        """Get git repository status.

        Args:
            path: Repository path (optional)

        Returns:
            Git status information
        """
        cwd = path or os.getcwd()

        try:
            result = subprocess.run(
                ["git", "status", "--porcelain", "-b"],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                return {"error": result.stderr, "status": "not_a_git_repo"}

            lines = result.stdout.strip().split('\n')
            branch_line = lines[0] if lines else ""
            status_lines = lines[1:] if len(lines) > 1 else []

            # Parse branch information
            branch = "unknown"
            if branch_line.startswith("## "):
                branch_info = branch_line[3:]
                if "..." in branch_info:
                    branch = branch_info.split("...")[0]
                else:
                    branch = branch_info.split()[0]

            # Parse file status
            modified = []
            staged = []
            untracked = []

            for line in status_lines:
                if len(line) >= 3:
                    status_code = line[:2]
                    filename = line[3:]

                    if status_code[0] in ['M', 'A', 'D', 'R', 'C']:
                        staged.append(filename)
                    if status_code[1] in ['M', 'D']:
                        modified.append(filename)
                    if status_code == '??':
                        untracked.append(filename)

            return {
                "branch": branch,
                "staged_files": staged,
                "modified_files": modified,
                "untracked_files": untracked,
                "is_clean": len(status_lines) == 0,
                "total_changes": len(staged) + len(modified) + len(untracked)
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git status timed out"}
        except FileNotFoundError:
            return {"error": "Git not found"}
        except Exception as e:
            return {"error": str(e)}

    def git_add(self, files: str, path: Optional[str] = None) -> Dict[str, Any]:
        """Add files to git staging area.

        Args:
            files: Files to add
            path: Repository path (optional)

        Returns:
            Git add result
        """
        cwd = path or os.getcwd()

        try:
            result = subprocess.run(
                ["git", "add"] + files.split(),
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=30
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "files_added": files
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def git_commit(self, message: str, path: Optional[str] = None) -> Dict[str, Any]:
        """Commit staged changes.

        Args:
            message: Commit message
            path: Repository path (optional)

        Returns:
            Git commit result
        """
        cwd = path or os.getcwd()

        try:
            result = subprocess.run(
                ["git", "commit", "-m", message],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=30
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "message": message
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def git_push(self, path: Optional[str] = None) -> Dict[str, Any]:
        """Push commits to remote repository.

        Args:
            path: Repository path (optional)

        Returns:
            Git push result
        """
        cwd = path or os.getcwd()

        try:
            result = subprocess.run(
                ["git", "push"],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=60
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def git_pull(self, path: Optional[str] = None) -> Dict[str, Any]:
        """Pull changes from remote repository.

        Args:
            path: Repository path (optional)

        Returns:
            Git pull result
        """
        cwd = path or os.getcwd()

        try:
            result = subprocess.run(
                ["git", "pull"],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=60
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def git_branch(self, path: Optional[str] = None) -> Dict[str, Any]:
        """List git branches.

        Args:
            path: Repository path (optional)

        Returns:
            Git branch information
        """
        cwd = path or os.getcwd()

        try:
            result = subprocess.run(
                ["git", "branch", "-a"],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                return {"error": result.stderr}

            branches = []
            current_branch = None

            for line in result.stdout.strip().split('\n'):
                line = line.strip()
                if line.startswith('* '):
                    current_branch = line[2:]
                    branches.append({"name": current_branch, "current": True})
                elif line:
                    branches.append({"name": line, "current": False})

            return {
                "branches": branches,
                "current_branch": current_branch,
                "total_branches": len(branches)
            }

        except Exception as e:
            return {"error": str(e)}

    # File Search Operations
    def find_files(self, pattern: str, path: Optional[str] = None, recursive: bool = True) -> Dict[str, Any]:
        """Find files matching pattern.

        Args:
            pattern: File pattern to search for
            path: Directory to search in
            recursive: Search recursively

        Returns:
            List of matching files
        """
        import glob

        search_path = Path(path) if path else Path.cwd()

        try:
            if recursive:
                glob_pattern = f"**/{pattern}"
                matches = list(search_path.glob(glob_pattern))
            else:
                matches = list(search_path.glob(pattern))

            files = []
            for match in matches:
                if match.is_file():
                    stat = match.stat()
                    files.append({
                        "path": str(match),
                        "name": match.name,
                        "size": stat.st_size,
                        "modified": stat.st_mtime,
                        "type": match.suffix.lower()
                    })

            return {
                "pattern": pattern,
                "search_path": str(search_path),
                "files": files,
                "total_found": len(files)
            }

        except Exception as e:
            return {"error": str(e), "pattern": pattern}

    def search_in_files(self, pattern: str, file_pattern: str = "*",
                       path: Optional[str] = None, case_sensitive: bool = False) -> Dict[str, Any]:
        """Search for text within files.

        Args:
            pattern: Text pattern to search for
            file_pattern: File pattern to search in
            path: Directory to search in
            case_sensitive: Case sensitive search

        Returns:
            Search results
        """
        import re

        search_path = Path(path) if path else Path.cwd()

        try:
            # Find files matching pattern
            matches = list(search_path.glob(f"**/{file_pattern}"))

            results = []
            flags = 0 if case_sensitive else re.IGNORECASE
            regex = re.compile(pattern, flags)

            for file_path in matches:
                if not file_path.is_file():
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()

                    file_matches = []
                    for line_num, line in enumerate(lines, 1):
                        if regex.search(line):
                            file_matches.append({
                                "line_number": line_num,
                                "line_content": line.strip(),
                                "match_positions": [m.span() for m in regex.finditer(line)]
                            })

                    if file_matches:
                        results.append({
                            "file": str(file_path),
                            "matches": file_matches,
                            "total_matches": len(file_matches)
                        })

                except Exception as e:
                    logger.debug(f"Error searching in {file_path}: {e}")
                    continue

            return {
                "pattern": pattern,
                "file_pattern": file_pattern,
                "search_path": str(search_path),
                "files_with_matches": results,
                "total_files_searched": len(matches),
                "total_files_with_matches": len(results)
            }

        except Exception as e:
            return {"error": str(e), "pattern": pattern}

    def replace_in_files(self, search_pattern: str, replace_text: str,
                        file_pattern: str = "*", path: Optional[str] = None,
                        case_sensitive: bool = False, dry_run: bool = True) -> Dict[str, Any]:
        """Replace text in files.

        Args:
            search_pattern: Text pattern to search for
            replace_text: Replacement text
            file_pattern: File pattern to search in
            path: Directory to search in
            case_sensitive: Case sensitive search
            dry_run: Only show what would be changed

        Returns:
            Replace operation results
        """
        import re

        search_path = Path(path) if path else Path.cwd()

        try:
            # Find files matching pattern
            matches = list(search_path.glob(f"**/{file_pattern}"))

            results = []
            flags = 0 if case_sensitive else re.IGNORECASE
            regex = re.compile(search_pattern, flags)

            for file_path in matches:
                if not file_path.is_file():
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    if regex.search(content):
                        new_content = regex.sub(replace_text, content)
                        changes = len(regex.findall(content))

                        if not dry_run:
                            # Create backup
                            backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                            shutil.copy2(file_path, backup_path)

                            # Write new content
                            with open(file_path, 'w', encoding='utf-8') as f:
                                f.write(new_content)

                        results.append({
                            "file": str(file_path),
                            "changes_made": changes,
                            "backup_created": not dry_run,
                            "backup_path": str(backup_path) if not dry_run else None
                        })

                except Exception as e:
                    logger.debug(f"Error processing {file_path}: {e}")
                    continue

            return {
                "search_pattern": search_pattern,
                "replace_text": replace_text,
                "file_pattern": file_pattern,
                "search_path": str(search_path),
                "dry_run": dry_run,
                "files_modified": results,
                "total_files_processed": len(matches),
                "total_files_modified": len(results)
            }

        except Exception as e:
            return {"error": str(e), "search_pattern": search_pattern}
