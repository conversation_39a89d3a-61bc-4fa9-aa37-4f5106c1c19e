and #!/bin/bash
# AI CLI Tool Installation Script

set -e

echo "🤖 AI CLI Tool Installation Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.8+ is installed
check_python() {
    print_status "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            print_success "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.8+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.8 or higher."
        exit 1
    fi
}

# Check if pip is installed
check_pip() {
    print_status "Checking pip installation..."
    
    if command -v pip3 &> /dev/null; then
        print_success "pip3 found"
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        print_success "pip found"
        PIP_CMD="pip"
    else
        print_error "pip not found. Please install pip."
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    if [ -f "requirements.txt" ]; then
        $PIP_CMD install -r requirements.txt
        print_success "Dependencies installed successfully"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Create configuration directory
setup_config() {
    print_status "Setting up configuration..."
    
    CONFIG_DIR="$HOME/.config/ai_cli_tool"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$HOME/.ai_cli_tool/logs"
    mkdir -p "$HOME/.ai_cli_tool/sessions"
    
    # Copy example config if it doesn't exist
    if [ ! -f "$CONFIG_DIR/config.yaml" ] && [ -f "config.yaml" ]; then
        cp config.yaml "$CONFIG_DIR/config.yaml"
        print_success "Configuration file created at $CONFIG_DIR/config.yaml"
    fi
    
    # Copy example .env if it doesn't exist
    if [ ! -f ".env" ] && [ -f ".env.example" ]; then
        cp .env.example .env
        print_warning "Please edit .env file with your API keys"
    fi
}

# Check optional dependencies
check_optional_deps() {
    print_status "Checking optional dependencies..."
    
    # Check for Ollama
    if command -v ollama &> /dev/null; then
        print_success "Ollama found - local LLM support available"
    else
        print_warning "Ollama not found - install from https://ollama.ai for local LLM support"
    fi
    
    # Check for Git
    if command -v git &> /dev/null; then
        print_success "Git found - git integration available"
    else
        print_warning "Git not found - some features may be limited"
    fi
    
    # Check for curl
    if command -v curl &> /dev/null; then
        print_success "curl found - web features available"
    else
        print_warning "curl not found - web search may be limited"
    fi
}

# Create launcher script
create_launcher() {
    print_status "Creating launcher script..."
    
    LAUNCHER_PATH="/usr/local/bin/ai-cli"
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # Try to create system-wide launcher
    if [ -w "/usr/local/bin" ]; then
        cat > "$LAUNCHER_PATH" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
$PYTHON_CMD main.py "\$@"
EOF
        chmod +x "$LAUNCHER_PATH"
        print_success "System launcher created at $LAUNCHER_PATH"
    else
        # Create user launcher
        USER_BIN="$HOME/.local/bin"
        mkdir -p "$USER_BIN"
        LAUNCHER_PATH="$USER_BIN/ai-cli"
        
        cat > "$LAUNCHER_PATH" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
$PYTHON_CMD main.py "\$@"
EOF
        chmod +x "$LAUNCHER_PATH"
        print_success "User launcher created at $LAUNCHER_PATH"
        print_warning "Make sure $USER_BIN is in your PATH"
    fi
}

# Run tests
run_tests() {
    if [ "$1" = "--with-tests" ]; then
        print_status "Running tests..."
        
        if command -v pytest &> /dev/null; then
            pytest tests/ -v
            print_success "All tests passed"
        else
            print_warning "pytest not found, skipping tests"
        fi
    fi
}

# Main installation process
main() {
    echo
    print_status "Starting installation process..."
    echo
    
    check_python
    check_pip
    install_dependencies
    setup_config
    check_optional_deps
    create_launcher
    run_tests "$1"
    
    echo
    print_success "🎉 Installation completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Edit your configuration: ai-cli config --edit"
    echo "2. Set up API keys in .env file (if using cloud LLMs)"
    echo "3. Start the tool: ai-cli"
    echo
    echo "For help: ai-cli --help"
    echo "For status: ai-cli status"
    echo
}

# Run main function with all arguments
main "$@"
