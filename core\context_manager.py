"""Context management for automatic environment awareness."""

import os
import fnmatch
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
import logging
import subprocess
from datetime import datetime

from models.schemas import ContextItem

logger = logging.getLogger(__name__)


class ContextManager:
    """Manages automatic context gathering from user environment."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize context manager.
        
        Args:
            config: Context configuration settings
        """
        self.config = config
        self.auto_gather = config.get("auto_gather", True)
        self.max_files = config.get("max_files", 20)
        self.max_file_size_kb = config.get("max_file_size_kb", 100)
        self.exclude_patterns = config.get("exclude_patterns", [])
        self.include_git_status = config.get("include_git_status", True)
        
        self.current_context: List[ContextItem] = []
        self.explicit_context: Set[str] = set()
    
    def gather_context(self, cwd: Optional[Path] = None) -> List[ContextItem]:
        """Gather context information from environment.
        
        Args:
            cwd: Current working directory (defaults to os.getcwd())
            
        Returns:
            List of context items
        """
        if not self.auto_gather:
            return self.current_context
        
        context_items = []
        
        if cwd is None:
            cwd = Path.cwd()
        
        try:
            # Add current directory info
            context_items.append(self._get_directory_context(cwd))
            
            # Add file listings
            context_items.extend(self._get_file_listings(cwd))
            
            # Add Git status if in a Git repository
            if self.include_git_status:
                git_context = self._get_git_context(cwd)
                if git_context:
                    context_items.append(git_context)
            
            # Add environment variables (selective)
            context_items.append(self._get_environment_context())
            
            # Add explicitly added context
            context_items.extend(self._get_explicit_context())
            
        except Exception as e:
            logger.error(f"Error gathering context: {e}")
        
        self.current_context = context_items
        return context_items
    
    def add_explicit_context(self, path: str) -> bool:
        """Add explicit context item.
        
        Args:
            path: File or directory path to add
            
        Returns:
            True if added successfully
        """
        try:
            path_obj = Path(path).resolve()
            if path_obj.exists():
                self.explicit_context.add(str(path_obj))
                logger.info(f"Added explicit context: {path}")
                return True
            else:
                logger.warning(f"Path does not exist: {path}")
                return False
        except Exception as e:
            logger.error(f"Error adding explicit context: {e}")
            return False
    
    def remove_explicit_context(self, path: str) -> bool:
        """Remove explicit context item.
        
        Args:
            path: Path to remove
            
        Returns:
            True if removed successfully
        """
        try:
            path_obj = Path(path).resolve()
            path_str = str(path_obj)
            if path_str in self.explicit_context:
                self.explicit_context.remove(path_str)
                logger.info(f"Removed explicit context: {path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error removing explicit context: {e}")
            return False
    
    def clear_explicit_context(self) -> None:
        """Clear all explicit context items."""
        self.explicit_context.clear()
        logger.info("Cleared all explicit context")
    
    def _get_directory_context(self, path: Path) -> ContextItem:
        """Get directory context information.
        
        Args:
            path: Directory path
            
        Returns:
            Context item for directory
        """
        try:
            stat = path.stat()
            return ContextItem(
                type="directory",
                path=str(path),
                content=f"Current directory: {path}",
                metadata={
                    "absolute_path": str(path.resolve()),
                    "exists": True,
                    "is_directory": True,
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Error getting directory context: {e}")
            return ContextItem(
                type="directory",
                path=str(path),
                content=f"Current directory: {path} (error accessing)",
                metadata={"error": str(e)}
            )
    
    def _get_file_listings(self, path: Path) -> List[ContextItem]:
        """Get file listings for directory.
        
        Args:
            path: Directory path
            
        Returns:
            List of context items for files
        """
        context_items = []
        
        try:
            files = []
            directories = []
            
            for item in path.iterdir():
                if self._should_exclude(item):
                    continue
                
                if item.is_file():
                    files.append(item)
                elif item.is_dir():
                    directories.append(item)
            
            # Sort and limit files
            files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            files = files[:self.max_files]
            
            # Create file listing context
            file_list = []
            for file in files:
                try:
                    stat = file.stat()
                    size_kb = stat.st_size / 1024
                    file_list.append({
                        "name": file.name,
                        "size_kb": round(size_kb, 2),
                        "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "extension": file.suffix
                    })
                except Exception as e:
                    logger.debug(f"Error getting file stats for {file}: {e}")
            
            if file_list:
                context_items.append(ContextItem(
                    type="file_listing",
                    path=str(path),
                    content=f"Files in {path}: {', '.join([f['name'] for f in file_list])}",
                    metadata={"files": file_list, "total_files": len(file_list)}
                ))
            
            # Add directory listing
            if directories:
                dir_names = [d.name for d in directories[:10]]  # Limit directory listing
                context_items.append(ContextItem(
                    type="directory_listing",
                    path=str(path),
                    content=f"Subdirectories: {', '.join(dir_names)}",
                    metadata={"directories": dir_names, "total_directories": len(directories)}
                ))
            
        except Exception as e:
            logger.error(f"Error getting file listings: {e}")
        
        return context_items
    
    def _get_git_context(self, path: Path) -> Optional[ContextItem]:
        """Get Git repository context.
        
        Args:
            path: Directory path to check
            
        Returns:
            Git context item or None
        """
        try:
            # Check if we're in a Git repository
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode != 0:
                return None
            
            # Get Git status
            status_result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            # Get current branch
            branch_result = subprocess.run(
                ["git", "branch", "--show-current"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            branch = branch_result.stdout.strip() if branch_result.returncode == 0 else "unknown"
            status_lines = status_result.stdout.strip().split('\n') if status_result.stdout.strip() else []
            
            # Parse status
            modified_files = []
            untracked_files = []
            staged_files = []
            
            for line in status_lines:
                if len(line) >= 3:
                    status_code = line[:2]
                    filename = line[3:]
                    
                    if status_code[0] in ['M', 'A', 'D', 'R', 'C']:
                        staged_files.append(filename)
                    if status_code[1] in ['M', 'D']:
                        modified_files.append(filename)
                    if status_code == '??':
                        untracked_files.append(filename)
            
            content_parts = [f"Git branch: {branch}"]
            if staged_files:
                content_parts.append(f"Staged: {len(staged_files)} files")
            if modified_files:
                content_parts.append(f"Modified: {len(modified_files)} files")
            if untracked_files:
                content_parts.append(f"Untracked: {len(untracked_files)} files")
            
            return ContextItem(
                type="git_status",
                path=str(path),
                content=", ".join(content_parts),
                metadata={
                    "branch": branch,
                    "staged_files": staged_files,
                    "modified_files": modified_files,
                    "untracked_files": untracked_files,
                    "is_clean": len(status_lines) == 0
                }
            )
            
        except Exception as e:
            logger.debug(f"Error getting Git context: {e}")
            return None
    
    def _get_environment_context(self) -> ContextItem:
        """Get relevant environment context.
        
        Returns:
            Environment context item
        """
        env_info = {
            "platform": os.name,
            "python_version": os.sys.version.split()[0],
            "user": os.getenv("USER") or os.getenv("USERNAME", "unknown"),
            "shell": os.getenv("SHELL", "unknown"),
            "term": os.getenv("TERM", "unknown")
        }
        
        return ContextItem(
            type="environment",
            content=f"Platform: {env_info['platform']}, Python: {env_info['python_version']}",
            metadata=env_info
        )
    
    def _get_explicit_context(self) -> List[ContextItem]:
        """Get explicitly added context items.
        
        Returns:
            List of explicit context items
        """
        context_items = []
        
        for path_str in self.explicit_context:
            try:
                path = Path(path_str)
                if path.is_file():
                    # Read file content if small enough
                    stat = path.stat()
                    size_kb = stat.st_size / 1024
                    
                    if size_kb <= self.max_file_size_kb:
                        try:
                            content = path.read_text(encoding='utf-8')
                            context_items.append(ContextItem(
                                type="file",
                                path=path_str,
                                content=content,
                                metadata={
                                    "size_kb": round(size_kb, 2),
                                    "encoding": "utf-8",
                                    "explicit": True
                                }
                            ))
                        except UnicodeDecodeError:
                            # Try with different encoding or skip binary files
                            context_items.append(ContextItem(
                                type="file",
                                path=path_str,
                                content=f"Binary file: {path.name}",
                                metadata={
                                    "size_kb": round(size_kb, 2),
                                    "binary": True,
                                    "explicit": True
                                }
                            ))
                    else:
                        context_items.append(ContextItem(
                            type="file",
                            path=path_str,
                            content=f"Large file: {path.name} ({size_kb:.1f} KB)",
                            metadata={
                                "size_kb": round(size_kb, 2),
                                "too_large": True,
                                "explicit": True
                            }
                        ))
                elif path.is_dir():
                    context_items.append(self._get_directory_context(path))
                    
            except Exception as e:
                logger.error(f"Error processing explicit context {path_str}: {e}")
        
        return context_items
    
    def _should_exclude(self, path: Path) -> bool:
        """Check if path should be excluded from context.
        
        Args:
            path: Path to check
            
        Returns:
            True if should be excluded
        """
        name = path.name
        
        for pattern in self.exclude_patterns:
            if fnmatch.fnmatch(name, pattern):
                return True
        
        return False
