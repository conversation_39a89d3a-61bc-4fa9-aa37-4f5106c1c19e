"""Integration tests for the AI CLI Tool."""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from core.orchestrator import Orchestrator
from core.config_manager import ConfigManager
from ui.cli_interface import CL<PERSON>nterface
from models.schemas import MessageR<PERSON>, Session


class TestIntegration:
    """Integration tests for the complete system."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config_path = self.temp_dir / "test_config.yaml"
        
        # Create test configuration
        test_config = {
            "llm": {
                "default_provider": "ollama",
                "ollama": {
                    "base_url": "http://localhost:11434",
                    "default_model": "llama2"
                },
                "deepseek": {
                    "base_url": "https://api.deepseek.com",
                    "default_model": "deepseek-chat"
                }
            },
            "context": {
                "auto_gather": True,
                "max_files": 10,
                "include_git_status": True
            },
            "ui": {
                "show_status_bar": True,
                "enable_animations": True
            }
        }
        
        import yaml
        with open(self.config_path, 'w') as f:
            yaml.dump(test_config, f)
    
    def teardown_method(self):
        """Cleanup test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_orchestrator_initialization(self):
        """Test that orchestrator initializes all components correctly."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # Check that all components are initialized
        assert orchestrator.config_manager is not None
        assert orchestrator.session_manager is not None
        assert orchestrator.context_manager is not None
        assert orchestrator.ai_core is not None
        assert orchestrator.tooling_engine is not None
        
        # Check configuration is loaded
        assert orchestrator.config_manager.get("llm.default_provider") == "ollama"
    
    def test_session_management_integration(self):
        """Test session management integration."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # Test session creation
        session = orchestrator.create_session("test_session")
        assert session.name == "test_session"
        assert session.session_id is not None
        
        # Test session listing
        sessions = orchestrator.list_sessions()
        assert len(sessions) >= 1
        assert any(s.name == "test_session" for s in sessions)
        
        # Test session switching
        success = orchestrator.switch_session(session.session_id)
        assert success is True
    
    def test_context_gathering_integration(self):
        """Test context gathering integration."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # Test context gathering
        context_items = orchestrator.get_context_items()
        assert isinstance(context_items, list)
        
        # Test adding explicit context
        test_file = self.temp_dir / "test.txt"
        test_file.write_text("test content")
        
        success = orchestrator.add_context_item(str(test_file))
        assert success is True
    
    def test_tooling_engine_integration(self):
        """Test tooling engine integration."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # Test tool schema retrieval
        schemas = orchestrator.tooling_engine.get_tool_schemas()
        assert isinstance(schemas, list)
        assert len(schemas) > 0
        
        # Test file operations
        test_file = self.temp_dir / "test_tool.txt"
        
        # Test write file
        result = orchestrator.tooling_engine.execute_tool(
            "write_file",
            {"path": str(test_file), "content": "test content"}
        )
        assert result.success is True
        assert test_file.exists()
        
        # Test read file
        result = orchestrator.tooling_engine.execute_tool(
            "read_file",
            {"path": str(test_file)}
        )
        assert result.success is True
        assert result.result == "test content"
    
    @patch('core.ai_core.OllamaAdapter')
    def test_ai_core_integration_mock(self, mock_ollama):
        """Test AI core integration with mocked LLM."""
        # Setup mock
        mock_adapter = Mock()
        mock_adapter.is_available.return_value = True
        mock_adapter.generate_response.return_value = Mock(
            content="Test response",
            tool_calls=[],
            model="test-model",
            usage={},
            finish_reason="stop"
        )
        mock_ollama.return_value = mock_adapter
        
        orchestrator = Orchestrator(str(self.config_path))
        
        # Test provider availability
        providers = orchestrator.get_available_providers()
        assert "ollama" in providers
        
        # Test response generation (would normally require LLM)
        # This tests the integration path without actual LLM calls
        assert orchestrator.ai_core is not None
    
    def test_cli_interface_initialization(self):
        """Test CLI interface initialization."""
        orchestrator = Orchestrator(str(self.config_path))
        ui_config = orchestrator.config_manager.get("ui", {})
        
        cli_interface = CLIInterface(ui_config, orchestrator)
        
        # Check components are initialized
        assert cli_interface.orchestrator is not None
        assert cli_interface.animation_manager is not None
        assert cli_interface.diff_viewer is not None
        assert cli_interface.console is not None
    
    def test_status_information_integration(self):
        """Test status information gathering."""
        orchestrator = Orchestrator(str(self.config_path))
        
        status_info = orchestrator.get_status_info()
        
        # Check required status fields
        required_fields = [
            "session_name", "session_id", "provider", 
            "available_providers", "context_items", "total_sessions",
            "current_directory", "config_path", "ollama_available", "deepseek_available"
        ]
        
        for field in required_fields:
            assert field in status_info
    
    def test_error_handling_integration(self):
        """Test error handling across components."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # Test invalid tool execution
        result = orchestrator.tooling_engine.execute_tool(
            "invalid_tool",
            {}
        )
        assert result.success is False
        assert "Unknown tool" in result.error
        
        # Test invalid file operations
        result = orchestrator.tooling_engine.execute_tool(
            "read_file",
            {"path": "/nonexistent/file.txt"}
        )
        assert result.success is False
    
    def test_configuration_integration(self):
        """Test configuration management integration."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # Test configuration access
        config_manager = orchestrator.config_manager
        assert config_manager.get("llm.default_provider") == "ollama"
        
        # Test configuration modification
        config_manager.set("test_key", "test_value")
        assert config_manager.get("test_key") == "test_value"
    
    def test_full_workflow_simulation(self):
        """Test a complete workflow simulation."""
        orchestrator = Orchestrator(str(self.config_path))
        
        # 1. Create session
        session = orchestrator.create_session("workflow_test")
        assert session is not None
        
        # 2. Add context
        test_file = self.temp_dir / "workflow_test.txt"
        test_file.write_text("workflow test content")
        success = orchestrator.add_context_item(str(test_file))
        assert success is True
        
        # 3. Get context items
        context_items = orchestrator.get_context_items()
        assert len(context_items) > 0
        
        # 4. Execute tool
        result = orchestrator.tooling_engine.execute_tool(
            "list_directory",
            {"path": str(self.temp_dir)}
        )
        assert result.success is True
        
        # 5. Check status
        status = orchestrator.get_status_info()
        assert status["session_name"] == "workflow_test"
        assert status["context_items"] > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
