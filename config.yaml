context:
  auto_gather: true
  exclude_patterns:
  - '*.pyc'
  - __pycache__
  - .git
  - node_modules
  - '*.log'
  include_git_status: true
  max_file_size_kb: 100
  max_files: 20
llm:
  deepseek:
    api_key: ***********************************
    base_url: https://api.deepseek.com/v1
    model: deepseek-reasoner
    timeout: 30
  default_provider: deepseek
  ollama:
    default_model: llama2
    server_url: http://localhost:11434
    timeout: 30
logging:
  backup_count: 5
  file_path: ~/.ai_cli_tool/logs/app.log
  level: INFO
  max_file_size_mb: 10
security:
  command_confirmation: true
  restricted_commands:
  - rm -rf
  - sudo rm
  - format
  - del /f /s /q
sessions:
  auto_save: true
  max_history_items: 100
  storage_path: ~/.ai_cli_tool/sessions
tools:
  file_operations:
    confirmation_for_destructive: true
    max_file_size_mb: 10
  shell:
    allow_sudo: true
    confirmation_required: true
    timeout: 60
  web_search:
    default_results: 5
    timeout: 15
ui:
  animation_speed: 0.1
  diff_context_lines: 3
  show_animations: true
  show_status_bar: true
  syntax_highlighting: true
