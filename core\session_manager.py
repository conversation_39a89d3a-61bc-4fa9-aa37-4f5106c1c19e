"""Session management for conversation history and state."""

import json
import uuid
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
import logging

from models.schemas import Session, Message, MessageRole

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages user sessions and conversation history."""
    
    def __init__(self, storage_path: Path):
        """Initialize session manager.
        
        Args:
            storage_path: Directory to store session files
        """
        self.storage_path = storage_path
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.current_session: Optional[Session] = None
        self.sessions: Dict[str, Session] = {}
        
        # Load existing sessions
        self._load_sessions()
        
        # Create default session if none exist
        if not self.sessions:
            self.create_session("default")
    
    def _load_sessions(self) -> None:
        """Load all sessions from storage."""
        try:
            for session_file in self.storage_path.glob("*.json"):
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    # Convert to Session object
                    session = Session(**session_data)
                    self.sessions[session.session_id] = session
                    
                    logger.debug(f"Loaded session: {session.session_id}")
                except Exception as e:
                    logger.error(f"Error loading session {session_file}: {e}")
        except Exception as e:
            logger.error(f"Error loading sessions: {e}")
    
    def create_session(self, name: Optional[str] = None) -> Session:
        """Create a new session.
        
        Args:
            name: Optional session name
            
        Returns:
            Created session
        """
        session_id = str(uuid.uuid4())
        session = Session(
            session_id=session_id,
            name=name or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        self.sessions[session_id] = session
        self.current_session = session
        self._save_session(session)
        
        logger.info(f"Created new session: {session_id} ({session.name})")
        return session
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """Get session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session or None if not found
        """
        return self.sessions.get(session_id)
    
    def list_sessions(self) -> List[Session]:
        """Get list of all sessions.
        
        Returns:
            List of sessions sorted by update time
        """
        return sorted(
            self.sessions.values(),
            key=lambda s: s.updated_at,
            reverse=True
        )
    
    def switch_session(self, session_id: str) -> bool:
        """Switch to a different session.
        
        Args:
            session_id: Session to switch to
            
        Returns:
            True if successful, False if session not found
        """
        session = self.sessions.get(session_id)
        if session:
            self.current_session = session
            logger.info(f"Switched to session: {session_id}")
            return True
        return False
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a session.
        
        Args:
            session_id: Session to delete
            
        Returns:
            True if successful, False if session not found
        """
        if session_id not in self.sessions:
            return False
        
        # Don't delete current session if it's the only one
        if len(self.sessions) == 1 and self.current_session and \
           self.current_session.session_id == session_id:
            logger.warning("Cannot delete the only remaining session")
            return False
        
        # Remove from memory
        session = self.sessions.pop(session_id)
        
        # Delete file
        session_file = self.storage_path / f"{session_id}.json"
        if session_file.exists():
            session_file.unlink()
        
        # Switch to another session if this was current
        if self.current_session and self.current_session.session_id == session_id:
            if self.sessions:
                self.current_session = next(iter(self.sessions.values()))
            else:
                self.current_session = None
        
        logger.info(f"Deleted session: {session_id}")
        return True
    
    def add_message(self, role: MessageRole, content: str, **kwargs) -> None:
        """Add a message to the current session.
        
        Args:
            role: Message role
            content: Message content
            **kwargs: Additional message fields
        """
        if not self.current_session:
            self.create_session()
        
        message = Message(
            role=role,
            content=content,
            **kwargs
        )
        
        self.current_session.messages.append(message)
        self.current_session.updated_at = datetime.now()
        
        # Trim history if too long
        max_history = 100  # Could be configurable
        if len(self.current_session.messages) > max_history:
            self.current_session.messages = self.current_session.messages[-max_history:]
        
        self._save_session(self.current_session)
    
    def get_conversation_history(self, limit: Optional[int] = None) -> List[Message]:
        """Get conversation history for current session.
        
        Args:
            limit: Maximum number of messages to return
            
        Returns:
            List of messages
        """
        if not self.current_session:
            return []
        
        messages = self.current_session.messages
        if limit:
            messages = messages[-limit:]
        
        return messages
    
    def update_context(self, context: Dict) -> None:
        """Update session context.
        
        Args:
            context: Context data to update
        """
        if not self.current_session:
            self.create_session()
        
        self.current_session.context.update(context)
        self.current_session.updated_at = datetime.now()
        self._save_session(self.current_session)
    
    def get_context(self) -> Dict:
        """Get current session context.
        
        Returns:
            Session context dictionary
        """
        if not self.current_session:
            return {}
        return self.current_session.context.copy()
    
    def _save_session(self, session: Session) -> None:
        """Save session to storage.
        
        Args:
            session: Session to save
        """
        try:
            session_file = self.storage_path / f"{session.session_id}.json"
            
            # Convert to dict for JSON serialization
            session_data = session.model_dump()
            
            # Convert datetime objects to ISO strings
            def convert_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime(item) for item in obj]
                return obj
            
            session_data = convert_datetime(session_data)
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Error saving session {session.session_id}: {e}")
    
    def cleanup_old_sessions(self, max_age_days: int = 30) -> int:
        """Clean up old inactive sessions.
        
        Args:
            max_age_days: Maximum age in days for inactive sessions
            
        Returns:
            Number of sessions cleaned up
        """
        cutoff_date = datetime.now().timestamp() - (max_age_days * 24 * 60 * 60)
        cleaned_count = 0
        
        sessions_to_delete = []
        for session in self.sessions.values():
            if session.updated_at.timestamp() < cutoff_date and not session.active:
                sessions_to_delete.append(session.session_id)
        
        for session_id in sessions_to_delete:
            if self.delete_session(session_id):
                cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} old sessions")
        return cleaned_count
