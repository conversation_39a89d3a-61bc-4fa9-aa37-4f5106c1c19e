# AI-Powered CLI Terminal Tool

A sophisticated command-line interface that integrates Large Language Models (LLMs) to provide intelligent assistance, automate tasks, and enhance developer productivity.

## 🚀 Features

### Core Capabilities
- **LLM Integration**: Support for Deepseek and Ollama (local) models
- **Natural Language Processing**: Interpret commands and queries in natural language
- **Function Calling**: Execute shell commands, file operations, and web searches
- **Automatic Context Awareness**: Gather environment context automatically
- **Session Management**: Persistent conversation history and state
- **Rich CLI Interface**: Beautiful terminal UI with animations and diff views

### Advanced Features
- **Autonomous Task Execution**: Break down complex requests into manageable steps
- **Cross-Platform Support**: Windows (WSL), macOS, and Linux
- **Diff Viewer**: Compare files and show changes with syntax highlighting
- **Git Integration**: Complete Git workflow (status, add, commit, push, pull, branch)
- **File Search & Replace**: Find files by pattern, search text, replace with backup
- **Extensible Architecture**: Modular design for easy feature additions
- **Security Features**: Command confirmation and restricted operations
- **Enhanced Tools**: 20+ built-in tools for comprehensive automation

## 📋 Requirements

- Python 3.8 or higher
- Rich terminal support (most modern terminals)
- Optional: Ollama for local LLM support
- Optional: Deepseek API key for cloud LLM support

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ai-cli-tool
```

### 2. Install Dependencies
```bash
# Using the built-in installer
python main.py install-deps

# Or manually with pip
pip install -r requirements.txt
```

### 3. Configuration

#### Option A: Interactive Setup
```bash
python main.py config --edit
```

#### Option B: Environment Variables
```bash
export DEEPSEEK_API_KEY="your-api-key-here"
```

#### Option C: Manual Configuration
Create `~/.config/ai_cli_tool/config.yaml`:
```yaml
llm:
  default_provider: "ollama"  # or "deepseek"
  deepseek:
    api_key: "your-api-key"
```

## 🚀 Quick Start

### Basic Usage
```bash
# Start the interactive CLI
python main.py

# Start with specific provider
python main.py --provider deepseek

# Start with specific session
python main.py --session my-project
```

### Example Commands
Once in the CLI, you can use natural language:

```
🤖 > list all python files in the current directory
🤖 > show me the git status
🤖 > create a new file called hello.py with a simple hello world function
🤖 > search the web for "python best practices"
🤖 > compare file1.py and file2.py
```

### Internal Commands
```
/help                    - Show help
/status                  - Show system status
/session list            - List all sessions
/session new my-project  - Create new session
/context add file.py     - Add file to context
/diff file1.py file2.py  - Show file differences
/provider list           - List available LLM providers
/exit                    - Exit the application
```

## 🔧 Configuration

### LLM Providers

#### Ollama (Local)
1. Install Ollama: https://ollama.ai/
2. Pull a model: `ollama pull llama2`
3. Configure in `config.yaml`:
```yaml
llm:
  default_provider: "ollama"
  ollama:
    server_url: "http://localhost:11434"
    default_model: "llama2"
```

#### Deepseek (Cloud)
1. Get API key from Deepseek
2. Set environment variable or configure:
```yaml
llm:
  default_provider: "deepseek"
  deepseek:
    api_key: "your-api-key"
    model: "deepseek-chat"
```

### Security Settings
```yaml
security:
  command_confirmation: true
  restricted_commands:
    - "rm -rf"
    - "sudo rm"
    - "format"
```

### UI Customization
```yaml
ui:
  show_animations: true
  animation_speed: 0.1
  show_status_bar: true
  syntax_highlighting: true
  diff_context_lines: 3
```

## 📖 Usage Examples

### File Operations
```
🤖 > read the contents of config.yaml
🤖 > create a backup of important.txt
🤖 > find all files modified in the last 24 hours
```

### Development Tasks
```
🤖 > run the tests and show me any failures
🤖 > format all python files in the src directory
🤖 > show me the git diff for the last commit
```

### System Administration
```
🤖 > check disk usage and show largest directories
🤖 > find all processes using port 8080
🤖 > show system resource usage
```

### Research and Information
```
🤖 > search for "docker best practices" and summarize the top results
🤖 > explain the differences between these two code files
🤖 > what are the latest Python features in version 3.12?
```

### Enhanced Git Operations
```
🤖 > show git status and stage all modified files
🤖 > commit changes with message "fix: resolve authentication issue"
🤖 > push changes to remote repository
🤖 > list all branches and show current branch
🤖 > pull latest changes from remote
```

### Advanced File Search
```
🤖 > find all Python files containing "TODO" comments
🤖 > search for "deprecated" in all JavaScript files
🤖 > replace "old_function" with "new_function" in all Python files (dry run)
🤖 > find all files larger than 1MB in the project
🤖 > locate all configuration files (*.yaml, *.json, *.toml)
```

## 🏗️ Architecture

The tool follows a modular architecture:

```
┌─────────────────┐
│   CLI Interface │  ← Rich terminal UI with animations
├─────────────────┤
│  Orchestrator   │  ← Central coordination layer
├─────────────────┤
│    AI Core      │  ← LLM integration and prompt engineering
├─────────────────┤
│ Tooling Engine  │  ← Tool execution (shell, files, web)
├─────────────────┤
│ Context Manager │  ← Environment awareness
├─────────────────┤
│Session Manager  │  ← Conversation history and state
└─────────────────┘
```

### Key Components

- **CLI Interface**: Rich terminal UI with animations and diff views
- **Orchestrator**: Central coordination and workflow management
- **AI Core**: LLM integration with Deepseek and Ollama support
- **Tooling Engine**: Execution of shell commands, file operations, web searches
- **Context Manager**: Automatic gathering of environment context
- **Session Manager**: Persistent conversation history and state management

## 🔒 Security Considerations

- **Command Confirmation**: Potentially dangerous commands require confirmation
- **Restricted Commands**: Configurable list of blocked command patterns
- **No Elevated Privileges**: Runs with user permissions only
- **Local Data**: Session data stored locally (when using Ollama)
- **API Privacy**: Be aware of data sent to cloud providers (Deepseek)

## 🐛 Troubleshooting

### Common Issues

#### "No LLM providers available"
- Ensure Ollama is running: `ollama serve`
- Check Deepseek API key is set correctly
- Verify network connectivity for cloud providers

#### "Command not found" errors
- Check if required tools are installed (git, curl, etc.)
- Verify PATH environment variable
- On Windows, ensure WSL is properly configured

#### Performance issues
- Use local Ollama for faster responses
- Reduce context size in configuration
- Check system resources

### Debug Mode
```bash
python main.py --log-level DEBUG
```

### Check Status
```bash
python main.py status
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Rich](https://github.com/Textualize/rich) for beautiful terminal output
- Uses [Typer](https://github.com/tiangolo/typer) for CLI framework
- Integrates with [Ollama](https://ollama.ai/) for local LLM support
- Supports [Deepseek](https://deepseek.com/) for cloud LLM capabilities

## 📞 Support

For issues, questions, or contributions:
- Open an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

---

**Happy coding with AI assistance! 🤖✨**
