"""Data models and schemas for the AI CLI tool."""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OLLAMA = "ollama"
    DEEPSEEK = "deepseek"


class ToolType(str, Enum):
    """Available tool types."""
    SHELL_COMMAND = "shell_command"
    FILE_OPERATION = "file_operation"
    WEB_SEARCH = "web_search"
    CONTEXT_QUERY = "context_query"


class MessageRole(str, Enum):
    """Message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class ToolCall(BaseModel):
    """Represents a tool call request from LLM."""
    tool_name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None


class ToolResult(BaseModel):
    """Result from tool execution."""
    tool_name: str
    success: bool
    result: Any
    error: Optional[str] = None
    execution_time: float
    call_id: Optional[str] = None


class Message(BaseModel):
    """A message in the conversation."""
    role: MessageRole
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    tool_calls: Optional[List[ToolCall]] = None
    tool_results: Optional[List[ToolResult]] = None
    metadata: Optional[Dict[str, Any]] = None


class Session(BaseModel):
    """User session data."""
    session_id: str
    name: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    messages: List[Message] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    active: bool = True


class ContextItem(BaseModel):
    """Context information item."""
    type: str  # "file", "directory", "git_status", "environment"
    path: Optional[str] = None
    content: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)


class LLMRequest(BaseModel):
    """Request to LLM."""
    prompt: str
    system_prompt: Optional[str] = None
    tools_schema: Optional[List[Dict[str, Any]]] = None
    context: Optional[List[ContextItem]] = None
    history: Optional[List[Message]] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None


class LLMResponse(BaseModel):
    """Response from LLM."""
    content: str
    tool_calls: Optional[List[ToolCall]] = None
    usage: Optional[Dict[str, int]] = None
    model: Optional[str] = None
    finish_reason: Optional[str] = None


class FileOperation(BaseModel):
    """File operation parameters."""
    operation: str  # "read", "write", "delete", "list", "create_dir", "move"
    path: str
    content: Optional[str] = None
    encoding: str = "utf-8"
    append: bool = False
    recursive: bool = False


class ShellCommand(BaseModel):
    """Shell command parameters."""
    command: str
    cwd: Optional[str] = None
    timeout: int = 60
    capture_output: bool = True
    shell: bool = True
    env: Optional[Dict[str, str]] = None


class WebSearchQuery(BaseModel):
    """Web search parameters."""
    query: str
    num_results: int = 5
    timeout: int = 15
    language: str = "en"


class DiffRequest(BaseModel):
    """Diff comparison request."""
    text1: str
    text2: str
    filename1: Optional[str] = None
    filename2: Optional[str] = None
    context_lines: int = 3


class AnimationState(BaseModel):
    """Animation state tracking."""
    active: bool = False
    start_time: Optional[datetime] = None
    current_frame: int = 0
    message: Optional[str] = None
