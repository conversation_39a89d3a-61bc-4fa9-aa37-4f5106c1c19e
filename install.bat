@echo off
REM AI CLI Tool Installation Script for Windows

echo 🤖 AI CLI Tool Installation Script
echo ==================================
echo.

REM Check if Python is installed
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found. Please install Python 3.8 or higher.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Python %PYTHON_VERSION% found

REM Check if pip is installed
echo [INFO] Checking pip installation...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] pip not found. Please install pip.
    pause
    exit /b 1
)
echo [SUCCESS] pip found

REM Install dependencies
echo [INFO] Installing Python dependencies...
if exist requirements.txt (
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed successfully
) else (
    echo [ERROR] requirements.txt not found
    pause
    exit /b 1
)

REM Create configuration directories
echo [INFO] Setting up configuration...
if not exist "%USERPROFILE%\.config\ai_cli_tool" mkdir "%USERPROFILE%\.config\ai_cli_tool"
if not exist "%USERPROFILE%\.ai_cli_tool\logs" mkdir "%USERPROFILE%\.ai_cli_tool\logs"
if not exist "%USERPROFILE%\.ai_cli_tool\sessions" mkdir "%USERPROFILE%\.ai_cli_tool\sessions"

REM Copy configuration files
if not exist "%USERPROFILE%\.config\ai_cli_tool\config.yaml" (
    if exist "config.yaml" (
        copy "config.yaml" "%USERPROFILE%\.config\ai_cli_tool\config.yaml" >nul
        echo [SUCCESS] Configuration file created
    )
)

if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [WARNING] Please edit .env file with your API keys
    )
)

REM Check optional dependencies
echo [INFO] Checking optional dependencies...

REM Check for Git
git --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Git found - git integration available
) else (
    echo [WARNING] Git not found - some features may be limited
)

REM Check for curl
curl --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] curl found - web features available
) else (
    echo [WARNING] curl not found - web search may be limited
)

REM Create batch launcher
echo [INFO] Creating launcher script...
set SCRIPT_DIR=%~dp0
echo @echo off > ai-cli.bat
echo cd /d "%SCRIPT_DIR%" >> ai-cli.bat
echo python main.py %%* >> ai-cli.bat

echo [SUCCESS] Launcher created: ai-cli.bat

echo.
echo [SUCCESS] 🎉 Installation completed successfully!
echo.
echo Next steps:
echo 1. Edit your configuration: python main.py config --edit
echo 2. Set up API keys in .env file (if using cloud LLMs)
echo 3. Start the tool: python main.py
echo    or use: ai-cli.bat
echo.
echo For help: python main.py --help
echo For status: python main.py status
echo.
echo Optional: Install Ollama for local LLM support from https://ollama.ai
echo.
pause
