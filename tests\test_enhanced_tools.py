"""Tests for enhanced tools in the tooling engine."""

import tempfile
import unittest
from pathlib import Path
import subprocess
import os

from core.tooling_engine import ToolingEngine


class TestEnhancedTools(unittest.TestCase):
    """Test enhanced tools functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.config = {
            "shell": {"timeout": 30},
            "file_operations": {"max_file_size_mb": 10},
            "web_search": {"default_results": 5}
        }
        self.engine = ToolingEngine(self.config)
    
    def test_find_files_tool(self):
        """Test find_files tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files
            (temp_path / "test1.py").write_text("print('hello')")
            (temp_path / "test2.txt").write_text("some text")
            (temp_path / "subdir").mkdir()
            (temp_path / "subdir" / "test3.py").write_text("print('world')")
            
            # Test finding Python files
            result = self.engine.execute_tool("find_files", {
                "pattern": "*.py",
                "path": str(temp_path),
                "recursive": True
            })
            
            self.assertTrue(result.success)
            self.assertIn("files", result.result)
            self.assertEqual(result.result["total_found"], 2)
            
            # Check that both Python files are found
            file_names = [f["name"] for f in result.result["files"]]
            self.assertIn("test1.py", file_names)
            self.assertIn("test3.py", file_names)
    
    def test_search_in_files_tool(self):
        """Test search_in_files tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files with content
            (temp_path / "file1.txt").write_text("Hello world\nThis is a test\nHello again")
            (temp_path / "file2.txt").write_text("No matches here")
            (temp_path / "file3.py").write_text("print('Hello Python')\n# Hello comment")
            
            # Search for "Hello"
            result = self.engine.execute_tool("search_in_files", {
                "pattern": "Hello",
                "file_pattern": "*",
                "path": str(temp_path),
                "case_sensitive": False
            })
            
            self.assertTrue(result.success)
            self.assertIn("files_with_matches", result.result)
            self.assertEqual(result.result["total_files_with_matches"], 2)
            
            # Check that matches are found in correct files
            files_with_matches = [f["file"] for f in result.result["files_with_matches"]]
            self.assertTrue(any("file1.txt" in f for f in files_with_matches))
            self.assertTrue(any("file3.py" in f for f in files_with_matches))
    
    def test_git_status_tool_no_repo(self):
        """Test git_status tool when not in a git repository."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self.engine.execute_tool("git_status", {"path": temp_dir})
            
            self.assertTrue(result.success)
            self.assertIn("error", result.result)
            self.assertIn("not_a_git_repo", result.result.get("status", ""))
    
    def test_git_status_tool_with_repo(self):
        """Test git_status tool in a git repository."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Initialize git repo
            try:
                subprocess.run(["git", "init"], cwd=temp_dir, check=True, capture_output=True)
                subprocess.run(["git", "config", "user.email", "<EMAIL>"], 
                             cwd=temp_dir, check=True, capture_output=True)
                subprocess.run(["git", "config", "user.name", "Test User"], 
                             cwd=temp_dir, check=True, capture_output=True)
                
                # Create a test file
                (temp_path / "test.txt").write_text("test content")
                
                result = self.engine.execute_tool("git_status", {"path": temp_dir})
                
                self.assertTrue(result.success)
                self.assertIn("branch", result.result)
                self.assertIn("untracked_files", result.result)
                self.assertIn("test.txt", result.result["untracked_files"])
                
            except (subprocess.CalledProcessError, FileNotFoundError):
                # Skip test if git is not available
                self.skipTest("Git not available")
    
    def test_git_add_tool(self):
        """Test git_add tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            try:
                # Initialize git repo
                subprocess.run(["git", "init"], cwd=temp_dir, check=True, capture_output=True)
                subprocess.run(["git", "config", "user.email", "<EMAIL>"], 
                             cwd=temp_dir, check=True, capture_output=True)
                subprocess.run(["git", "config", "user.name", "Test User"], 
                             cwd=temp_dir, check=True, capture_output=True)
                
                # Create a test file
                (temp_path / "test.txt").write_text("test content")
                
                # Add file using tool
                result = self.engine.execute_tool("git_add", {
                    "files": "test.txt",
                    "path": temp_dir
                })
                
                self.assertTrue(result.success)
                self.assertTrue(result.result["success"])
                self.assertEqual(result.result["files_added"], "test.txt")
                
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.skipTest("Git not available")
    
    def test_replace_in_files_dry_run(self):
        """Test replace_in_files tool with dry run."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files
            (temp_path / "file1.txt").write_text("Hello world\nHello Python")
            (temp_path / "file2.py").write_text("print('Hello')\nprint('Hello again')")
            
            # Test dry run replacement
            result = self.engine.execute_tool("replace_in_files", {
                "search_pattern": "Hello",
                "replace_text": "Hi",
                "file_pattern": "*",
                "path": str(temp_path),
                "dry_run": True
            })
            
            self.assertTrue(result.success)
            self.assertTrue(result.result["dry_run"])
            self.assertEqual(result.result["total_files_modified"], 2)
            
            # Verify original files are unchanged
            self.assertEqual((temp_path / "file1.txt").read_text(), "Hello world\nHello Python")
            self.assertEqual((temp_path / "file2.py").read_text(), "print('Hello')\nprint('Hello again')")
    
    def test_tool_schemas_include_new_tools(self):
        """Test that tool schemas include the new tools."""
        schemas = self.engine.get_tool_schemas()
        tool_names = [schema["name"] for schema in schemas]
        
        # Check that new tools are included
        expected_tools = [
            "git_status", "git_add", "git_commit", 
            "find_files", "search_in_files"
        ]
        
        for tool in expected_tools:
            self.assertIn(tool, tool_names, f"Tool {tool} not found in schemas")
    
    def test_unknown_enhanced_tool(self):
        """Test handling of unknown enhanced tool."""
        result = self.engine.execute_tool("nonexistent_tool", {})
        
        self.assertFalse(result.success)
        self.assertIn("Unknown tool", result.error)


if __name__ == "__main__":
    unittest.main()
