"""Central orchestration layer for the AI CLI tool."""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from models.schemas import MessageR<PERSON>, Message, ToolCall, ToolResult
from core.config_manager import ConfigManager
from core.session_manager import SessionManager
from core.context_manager import ContextManager
from core.ai_core import <PERSON><PERSON><PERSON>
from core.tooling_engine import ToolingEngine

logger = logging.getLogger(__name__)


class Orchestrator:
    """Central orchestration layer that coordinates all components."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the orchestrator.
        
        Args:
            config_path: Path to configuration file
        """
        # Initialize configuration
        self.config_manager = ConfigManager(config_path)
        
        # Initialize session manager
        sessions_path = self.config_manager.get_storage_path("sessions.storage_path")
        self.session_manager = SessionManager(sessions_path)
        
        # Initialize context manager
        context_config = self.config_manager.get("context", {})
        self.context_manager = ContextManager(context_config)
        
        # Initialize tooling engine
        tools_config = self.config_manager.get("tools", {})
        self.tooling_engine = ToolingEngine(tools_config)
        
        # Initialize AI core
        llm_config = self.config_manager.get("llm", {})
        self.ai_core = AICore(llm_config, self.config_manager)

        # Set current provider and validate availability
        default_provider = llm_config.get("default_provider", "ollama")
        available_providers = self.ai_core.get_available_providers()

        if default_provider in available_providers:
            self.ai_core.set_provider(default_provider)
            logger.info(f"Set default provider to: {default_provider}")
        elif available_providers:
            # Use first available provider
            fallback_provider = available_providers[0]
            self.ai_core.set_provider(fallback_provider)
            logger.warning(f"Default provider '{default_provider}' not available, using: {fallback_provider}")
        else:
            logger.warning("No LLM providers are available")

        # State
        self.current_context = []

        logger.info("Orchestrator initialized successfully")
    
    def process_input(self, user_input: str) -> Dict[str, Any]:
        """Process user input and return response.
        
        Args:
            user_input: User's input string
            
        Returns:
            Response dictionary
        """
        try:
            # Add user message to session
            self.session_manager.add_message(MessageRole.USER, user_input)
            
            # Gather context
            self.current_context = self.context_manager.gather_context()
            
            # Get conversation history
            history = self.session_manager.get_conversation_history(limit=10)
            
            # Get available tools
            tools_schema = self.tooling_engine.get_tool_schemas()
            
            # Generate AI response
            ai_response = self.ai_core.generate_response(
                prompt=user_input,
                context=self.current_context,
                history=history,
                tools_schema=tools_schema
            )
            
            # Process tool calls if any
            tool_results = []
            if ai_response.tool_calls:
                tool_results = self._execute_tool_calls(ai_response.tool_calls)
                
                # If tools were executed, get a follow-up response
                if tool_results:
                    # Add tool results to context for follow-up
                    tool_context = self._format_tool_results_for_context(tool_results)
                    
                    follow_up_response = self.ai_core.generate_response(
                        prompt=f"Based on the tool execution results, provide a summary and answer to the user's original question: {user_input}",
                        context=self.current_context,
                        history=history + [Message(
                            role=MessageRole.ASSISTANT,
                            content=ai_response.content,
                            tool_calls=ai_response.tool_calls,
                            tool_results=tool_results
                        )]
                    )
                    
                    final_content = follow_up_response.content
                else:
                    final_content = ai_response.content
            else:
                final_content = ai_response.content
            
            # Add assistant response to session
            self.session_manager.add_message(
                MessageRole.ASSISTANT,
                final_content,
                tool_calls=ai_response.tool_calls,
                tool_results=tool_results
            )
            
            # Prepare response
            response = {
                "content": final_content,
                "tool_results": [result.model_dump() for result in tool_results],
                "context_items": len(self.current_context),
                "model_used": ai_response.model
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing input: {e}")
            return {"error": str(e)}

    def process_input_stream(self, user_input: str):
        """Process user input and stream response.

        Args:
            user_input: User's input string

        Yields:
            Response chunks
        """
        try:
            # Add user message to session
            self.session_manager.add_message(MessageRole.USER, user_input)

            # Gather context
            self.current_context = self.context_manager.gather_context()

            # Get conversation history
            history = self.session_manager.get_conversation_history(limit=10)

            # Get available tools
            tools_schema = self.tooling_engine.get_tool_schemas()

            # Create request
            from models.schemas import LLMRequest
            system_prompt = self.ai_core._create_system_prompt(tools_schema is not None)

            request = LLMRequest(
                prompt=user_input,
                system_prompt=system_prompt,
                context=self.current_context,
                history=history,
                tools_schema=tools_schema
            )

            # Stream AI response
            full_content = ""
            for chunk in self.ai_core.adapters[self.ai_core.current_provider].stream_response(request):
                full_content += chunk
                yield chunk

            # Add assistant response to session
            self.session_manager.add_message(
                MessageRole.ASSISTANT,
                full_content
            )

        except Exception as e:
            logger.error(f"Error streaming input: {e}")
            yield f"Error: {str(e)}"
    
    def _execute_tool_calls(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """Execute tool calls and return results.
        
        Args:
            tool_calls: List of tool calls to execute
            
        Returns:
            List of tool results
        """
        results = []
        
        for tool_call in tool_calls:
            try:
                # Check if confirmation is needed for this tool
                if self._needs_confirmation(tool_call):
                    # In a real implementation, you'd prompt the user
                    # For now, we'll assume confirmation is given
                    logger.info(f"Tool execution: {tool_call.tool_name} with args {tool_call.arguments}")
                
                # Execute the tool
                result = self.tooling_engine.execute_tool(
                    tool_call.tool_name,
                    tool_call.arguments
                )
                
                result.call_id = tool_call.call_id
                results.append(result)
                
                logger.info(f"Tool {tool_call.tool_name} executed: success={result.success}")
                
            except Exception as e:
                logger.error(f"Error executing tool {tool_call.tool_name}: {e}")
                results.append(ToolResult(
                    tool_name=tool_call.tool_name,
                    success=False,
                    result=None,
                    error=str(e),
                    execution_time=0.0,
                    call_id=tool_call.call_id
                ))
        
        return results
    
    def _needs_confirmation(self, tool_call: ToolCall) -> bool:
        """Check if tool call needs user confirmation.
        
        Args:
            tool_call: Tool call to check
            
        Returns:
            True if confirmation needed
        """
        # Check configuration for confirmation requirements
        if tool_call.tool_name == "shell_command":
            return self.config_manager.get("tools.shell.confirmation_required", True)
        
        elif tool_call.tool_name in ["delete_file", "delete_directory", "write_file"]:
            return self.config_manager.get("tools.file_operations.confirmation_for_destructive", True)
        
        return False
    
    def _format_tool_results_for_context(self, tool_results: List[ToolResult]) -> str:
        """Format tool results for AI context.
        
        Args:
            tool_results: List of tool results
            
        Returns:
            Formatted context string
        """
        context_parts = []
        
        for result in tool_results:
            if result.success:
                context_parts.append(f"Tool {result.tool_name} succeeded: {result.result}")
            else:
                context_parts.append(f"Tool {result.tool_name} failed: {result.error}")
        
        return "\n".join(context_parts)
    
    # Session management methods
    def create_session(self, name: Optional[str] = None):
        """Create a new session."""
        return self.session_manager.create_session(name)
    
    def list_sessions(self):
        """List all sessions."""
        return self.session_manager.list_sessions()
    
    def switch_session(self, session_id: str) -> bool:
        """Switch to a different session."""
        return self.session_manager.switch_session(session_id)
    
    def get_conversation_history(self):
        """Get conversation history for current session."""
        return self.session_manager.get_conversation_history()
    
    # Context management methods
    def get_context_items(self):
        """Get current context items."""
        # Ensure context is up to date
        self.current_context = self.context_manager.gather_context()
        return self.current_context
    
    def add_context_item(self, path: str) -> bool:
        """Add explicit context item."""
        return self.context_manager.add_explicit_context(path)
    
    def clear_explicit_context(self):
        """Clear explicit context."""
        self.context_manager.clear_explicit_context()
    
    # Provider management methods
    def get_available_providers(self) -> List[str]:
        """Get available LLM providers."""
        return self.ai_core.get_available_providers()
    
    def get_current_provider(self) -> str:
        """Get current LLM provider."""
        return self.ai_core.current_provider
    
    def set_provider(self, provider: str) -> bool:
        """Set LLM provider."""
        return self.ai_core.set_provider(provider)
    
    # Status information
    def get_status_info(self) -> Dict[str, Any]:
        """Get comprehensive status information.
        
        Returns:
            Status information dictionary
        """
        current_session = self.session_manager.current_session
        
        return {
            "session_name": current_session.name if current_session else "none",
            "session_id": current_session.session_id if current_session else "none",
            "provider": self.ai_core.current_provider,
            "available_providers": self.ai_core.get_available_providers(),
            "context_items": len(self.current_context),
            "total_sessions": len(self.session_manager.sessions),
            "current_directory": str(Path.cwd()),
            "config_path": str(self.config_manager.config_path),
            "ollama_available": "ollama" in self.ai_core.adapters and self.ai_core.adapters["ollama"].is_available(),
            "deepseek_available": "deepseek" in self.ai_core.adapters and self.ai_core.adapters["deepseek"].is_available()
        }

    def shutdown(self) -> None:
        """Shutdown the orchestrator and cleanup resources."""
        try:
            logger.info("Shutting down orchestrator...")

            # Save current session if active
            if self.session_manager.current_session:
                self.session_manager._save_session(self.session_manager.current_session)

            # Cleanup any resources
            # Additional cleanup can be added here as needed

            # Cleanup old sessions
            self.session_manager.cleanup_old_sessions()

            logger.info("Orchestrator shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
