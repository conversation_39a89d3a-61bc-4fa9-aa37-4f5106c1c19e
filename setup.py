#!/usr/bin/env python3
"""Setup script for AI CLI Tool."""

from setuptools import setup, find_packages
from pathlib import Path

# Read the contents of README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements = []
requirements_file = this_directory / "requirements.txt"
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')

setup(
    name="ai-cli-tool",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="AI-Powered CLI Terminal Tool with LLM integration",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/ai-cli-tool",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ai-cli=main:app",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"],
    },
    keywords="ai cli terminal llm assistant automation",
    project_urls={
        "Bug Reports": "https://github.com/example/ai-cli-tool/issues",
        "Source": "https://github.com/example/ai-cli-tool",
        "Documentation": "https://github.com/example/ai-cli-tool#readme",
    },
)
