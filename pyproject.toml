[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-cli-tool"
version = "1.0.0"
description = "AI-Powered CLI Terminal Tool with LLM integration"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AI Assistant", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Shells",
    "Topic :: Utilities",
]
keywords = ["ai", "cli", "terminal", "llm", "assistant", "automation"]
requires-python = ">=3.8"
dependencies = [
    "rich>=13.7.0",
    "prompt-toolkit>=3.0.43",
    "typer>=0.9.0",
    "pydantic>=2.5.0",
    "pyyaml>=6.0.1",
    "requests>=2.31.0",
    "httpx>=0.25.0",
    "beautifulsoup4>=4.12.0",
    "trafilatura>=1.6.0",
    "python-dotenv>=1.0.0",
    "gitpython>=3.1.40",
    "psutil>=5.9.0",
    "aiofiles>=23.2.0",
    "asyncio-throttle>=1.0.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/example/ai-cli-tool"
Documentation = "https://github.com/example/ai-cli-tool#readme"
Repository = "https://github.com/example/ai-cli-tool.git"
"Bug Tracker" = "https://github.com/example/ai-cli-tool/issues"

[project.scripts]
ai-cli = "main:app"

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "ui*", "models*", "utils*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "trafilatura.*",
    "prompt_toolkit.*",
    "rich.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=core",
    "--cov=ui",
    "--cov=models",
    "--cov=utils",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["core", "ui", "models", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
