"""Animation components for the CLI interface."""

import time
import threading
from datetime import datetime, timedelta
from typing import Optional, Callable
from rich.console import Console
from rich.live import Live
from rich.text import Text
from rich.panel import Panel
from rich.align import Align

from models.schemas import AnimationState


class BallAnimation:
    """Ball animation with elapsed timer for processing indication."""
    
    def __init__(self, console: Console, message: str = "Processing..."):
        """Initialize ball animation.
        
        Args:
            console: Rich console instance
            message: Message to display with animation
        """
        self.console = console
        self.message = message
        self.state = AnimationState()
        
        # Animation frames - bouncing ball effect
        self.frames = [
            "( ●    )",
            "(  ●   )",
            "(   ●  )",
            "(    ● )",
            "(     ●)",
            "(    ● )",
            "(   ●  )",
            "(  ●   )",
        ]
        
        self.frame_delay = 0.1  # seconds between frames
        self._stop_event = threading.Event()
        self._thread: Optional[threading.Thread] = None
        self._live: Optional[Live] = None
    
    def start(self) -> None:
        """Start the animation."""
        if self.state.active:
            return
        
        self.state.active = True
        self.state.start_time = datetime.now()
        self.state.current_frame = 0
        self.state.message = self.message
        
        self._stop_event.clear()
        self._thread = threading.Thread(target=self._animate, daemon=True)
        self._thread.start()
    
    def stop(self) -> None:
        """Stop the animation."""
        if not self.state.active:
            return

        self.state.active = False
        self._stop_event.set()

        # Stop live display first
        if self._live:
            try:
                self._live.stop()
            except Exception:
                pass  # Ignore errors during cleanup
            self._live = None

        # Wait for thread to finish
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=2.0)
            if self._thread.is_alive():
                # Force cleanup if thread doesn't stop
                self._thread = None
    
    def update_message(self, message: str) -> None:
        """Update the animation message.
        
        Args:
            message: New message to display
        """
        self.state.message = message
    
    def _animate(self) -> None:
        """Animation loop (runs in separate thread)."""
        try:
            with Live(
                self._create_display(),
                console=self.console,
                refresh_per_second=10,
                transient=True
            ) as live:
                self._live = live

                while not self._stop_event.is_set():
                    try:
                        live.update(self._create_display())

                        # Update frame
                        self.state.current_frame = (self.state.current_frame + 1) % len(self.frames)

                        time.sleep(self.frame_delay)
                    except Exception:
                        # Continue animation even if individual frame fails
                        time.sleep(self.frame_delay)

        except Exception as e:
            # Silently handle animation errors to avoid disrupting main flow
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"Animation error: {e}")
        finally:
            self._live = None
    
    def _create_display(self) -> Panel:
        """Create the animation display.
        
        Returns:
            Rich panel with animation and timer
        """
        # Calculate elapsed time
        if self.state.start_time:
            elapsed = datetime.now() - self.state.start_time
            elapsed_str = self._format_elapsed_time(elapsed)
        else:
            elapsed_str = "00:00"
        
        # Get current frame
        frame = self.frames[self.state.current_frame]
        
        # Create display text
        display_text = Text()
        display_text.append(frame, style="cyan bold")
        display_text.append("  ")
        display_text.append(self.state.message or "Processing...", style="white")
        display_text.append("  ")
        display_text.append(f"[{elapsed_str}]", style="dim")
        
        return Panel(
            Align.center(display_text),
            border_style="blue",
            padding=(0, 1)
        )
    
    def _format_elapsed_time(self, elapsed: timedelta) -> str:
        """Format elapsed time as MM:SS.
        
        Args:
            elapsed: Elapsed timedelta
            
        Returns:
            Formatted time string
        """
        total_seconds = int(elapsed.total_seconds())
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes:02d}:{seconds:02d}"


class SpinnerAnimation:
    """Simple spinner animation for lighter processing indication."""
    
    def __init__(self, console: Console, message: str = "Working..."):
        """Initialize spinner animation.
        
        Args:
            console: Rich console instance
            message: Message to display
        """
        self.console = console
        self.message = message
        self.state = AnimationState()
        
        # Spinner frames
        self.frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        self.frame_delay = 0.08
        
        self._stop_event = threading.Event()
        self._thread: Optional[threading.Thread] = None
        self._live: Optional[Live] = None
    
    def start(self) -> None:
        """Start the spinner."""
        if self.state.active:
            return
        
        self.state.active = True
        self.state.start_time = datetime.now()
        self.state.current_frame = 0
        self.state.message = self.message
        
        self._stop_event.clear()
        self._thread = threading.Thread(target=self._animate, daemon=True)
        self._thread.start()
    
    def stop(self) -> None:
        """Stop the spinner."""
        if not self.state.active:
            return

        self.state.active = False
        self._stop_event.set()

        # Stop live display first
        if self._live:
            try:
                self._live.stop()
            except Exception:
                pass  # Ignore errors during cleanup
            self._live = None

        # Wait for thread to finish
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=2.0)
            if self._thread.is_alive():
                # Force cleanup if thread doesn't stop
                self._thread = None
    
    def update_message(self, message: str) -> None:
        """Update spinner message."""
        self.state.message = message
    
    def _animate(self) -> None:
        """Spinner animation loop."""
        try:
            with Live(
                self._create_display(),
                console=self.console,
                refresh_per_second=12,
                transient=True
            ) as live:
                self._live = live

                while not self._stop_event.is_set():
                    try:
                        live.update(self._create_display())
                        self.state.current_frame = (self.state.current_frame + 1) % len(self.frames)
                        time.sleep(self.frame_delay)
                    except Exception:
                        # Continue animation even if individual frame fails
                        time.sleep(self.frame_delay)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"Spinner animation error: {e}")
        finally:
            self._live = None
    
    def _create_display(self) -> Text:
        """Create spinner display."""
        frame = self.frames[self.state.current_frame]
        
        display_text = Text()
        display_text.append(frame, style="cyan bold")
        display_text.append(" ")
        display_text.append(self.state.message or "Working...", style="white")
        
        return display_text


class ProgressAnimation:
    """Progress bar animation for longer operations."""
    
    def __init__(self, console: Console, total_steps: int, description: str = "Progress"):
        """Initialize progress animation.
        
        Args:
            console: Rich console instance
            total_steps: Total number of steps
            description: Progress description
        """
        self.console = console
        self.total_steps = total_steps
        self.description = description
        self.current_step = 0
        
        self.state = AnimationState()
        self._live: Optional[Live] = None
    
    def start(self) -> None:
        """Start progress display."""
        if self.state.active:
            return
        
        self.state.active = True
        self.state.start_time = datetime.now()
        
        self._live = Live(
            self._create_display(),
            console=self.console,
            refresh_per_second=4,
            transient=True
        )
        self._live.start()
    
    def update(self, step: int, message: str = "") -> None:
        """Update progress.
        
        Args:
            step: Current step number
            message: Optional status message
        """
        self.current_step = min(step, self.total_steps)
        if message:
            self.state.message = message
        
        if self._live:
            self._live.update(self._create_display())
    
    def stop(self) -> None:
        """Stop progress display."""
        if not self.state.active:
            return

        self.state.active = False

        if self._live:
            try:
                self._live.stop()
            except Exception:
                pass  # Ignore errors during cleanup
            self._live = None
    
    def _create_display(self) -> Panel:
        """Create progress display."""
        # Calculate progress percentage
        if self.total_steps > 0:
            progress = self.current_step / self.total_steps
        else:
            progress = 0
        
        # Create progress bar
        bar_width = 40
        filled_width = int(bar_width * progress)
        bar = "█" * filled_width + "░" * (bar_width - filled_width)
        
        # Calculate elapsed time and ETA
        if self.state.start_time:
            elapsed = datetime.now() - self.state.start_time
            elapsed_str = self._format_elapsed_time(elapsed)
            
            if progress > 0:
                eta = elapsed / progress - elapsed
                eta_str = self._format_elapsed_time(eta)
            else:
                eta_str = "--:--"
        else:
            elapsed_str = "00:00"
            eta_str = "--:--"
        
        # Create display
        display_text = Text()
        display_text.append(f"{self.description}: ", style="white bold")
        display_text.append(f"[{bar}] ", style="cyan")
        display_text.append(f"{self.current_step}/{self.total_steps} ", style="white")
        display_text.append(f"({progress:.1%}) ", style="green")
        display_text.append(f"Elapsed: {elapsed_str} ", style="dim")
        display_text.append(f"ETA: {eta_str}", style="dim")
        
        if self.state.message:
            display_text.append(f"\n{self.state.message}", style="yellow")
        
        return Panel(
            display_text,
            border_style="blue",
            padding=(0, 1)
        )
    
    def _format_elapsed_time(self, elapsed: timedelta) -> str:
        """Format elapsed time."""
        total_seconds = int(elapsed.total_seconds())
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes:02d}:{seconds:02d}"


class AnimationManager:
    """Manages different types of animations."""
    
    def __init__(self, console: Console, config: dict):
        """Initialize animation manager.
        
        Args:
            console: Rich console instance
            config: UI configuration
        """
        self.console = console
        self.config = config
        self.show_animations = config.get("show_animations", True)
        self.current_animation: Optional[object] = None
    
    def start_ball_animation(self, message: str = "Processing...") -> Optional[BallAnimation]:
        """Start ball animation.
        
        Args:
            message: Animation message
            
        Returns:
            Animation instance or None if disabled
        """
        if not self.show_animations:
            return None
        
        self.stop_current_animation()
        
        animation = BallAnimation(self.console, message)
        animation.start()
        self.current_animation = animation
        return animation
    
    def start_spinner(self, message: str = "Working...") -> Optional[SpinnerAnimation]:
        """Start spinner animation.
        
        Args:
            message: Animation message
            
        Returns:
            Animation instance or None if disabled
        """
        if not self.show_animations:
            return None
        
        self.stop_current_animation()
        
        animation = SpinnerAnimation(self.console, message)
        animation.start()
        self.current_animation = animation
        return animation
    
    def start_progress(self, total_steps: int, description: str = "Progress") -> Optional[ProgressAnimation]:
        """Start progress animation.
        
        Args:
            total_steps: Total steps
            description: Progress description
            
        Returns:
            Animation instance or None if disabled
        """
        if not self.show_animations:
            return None
        
        self.stop_current_animation()
        
        animation = ProgressAnimation(self.console, total_steps, description)
        animation.start()
        self.current_animation = animation
        return animation
    
    def stop_current_animation(self) -> None:
        """Stop current animation."""
        if self.current_animation:
            try:
                if hasattr(self.current_animation, 'stop'):
                    self.current_animation.stop()
            except Exception as e:
                # Log but don't raise - we want to ensure cleanup happens
                import logging
                logger = logging.getLogger(__name__)
                logger.debug(f"Error stopping animation: {e}")
            finally:
                self.current_animation = None

        # Give a moment for cleanup
        import time
        time.sleep(0.05)
