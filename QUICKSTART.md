# Quick Start Guide

Get up and running with the AI CLI Tool in minutes!

## 🚀 Installation

### Windows
```cmd
# Run the installation script
install.bat

# Or install manually
pip install -r requirements.txt
python main.py --help
```

### Linux/macOS
```bash
# Run the installation script
chmod +x install.sh
./install.sh

# Or install manually
pip install -r requirements.txt
python main.py --help
```

## ⚙️ Configuration

### 1. Choose Your LLM Provider

#### Option A: Ollama (Local, Free)
1. Install Ollama: https://ollama.ai/
2. Pull a model: `ollama pull llama2`
3. Start Ollama: `ollama serve`

#### Option B: Deepseek (Cloud, API Key Required)
1. Get API key from Deepseek
2. Set environment variable: `export DEEPSEEK_API_KEY="your-key"`
3. Or edit `.env` file

### 2. First Run
```bash
# Start the tool
python main.py

# Check status
python main.py status

# Get help
python main.py --help
```

## 🎯 Basic Usage

### Natural Language Commands
```
🤖 > list all python files in this directory
🤖 > show me the git status
🤖 > create a hello world script in Python
🤖 > search the web for "python best practices"
```

### Internal Commands
```
/help                    # Show all commands
/status                  # System status
/session list            # List sessions
/context add file.py     # Add file to context
/diff file1.py file2.py  # Compare files
/exit                    # Exit
```

## 📝 Example Session

```
🤖 > What files are in the current directory?

I'll list the files in your current directory.

✓ list_directory
Files found:
- main.py (Python script, 15.2 KB)
- config.yaml (Configuration, 2.1 KB)
- README.md (Markdown, 8.5 KB)
- requirements.txt (Text, 0.5 KB)

🤖 > Show me the first 10 lines of main.py

✓ read_file
Here are the first 10 lines of main.py:

#!/usr/bin/env python3
"""
AI-Powered CLI Terminal Tool
...

🤖 > Create a simple test file

✓ write_file
I've created a test file called 'test_example.py' with a simple Python script.

🤖 > /diff main.py test_example.py

[Shows colorized diff between the files]
```

## 🔧 Configuration Tips

### Performance
- Use Ollama for faster local responses
- Reduce context size for better performance
- Enable/disable animations based on preference

### Security
- Review commands before execution
- Use confirmation for destructive operations
- Keep API keys secure

### Customization
```yaml
# ~/.config/ai_cli_tool/config.yaml
ui:
  show_animations: true
  syntax_highlighting: true
  
tools:
  shell:
    confirmation_required: true
    
context:
  max_files: 20
  auto_gather: true
```

## 🐛 Troubleshooting

### Common Issues

**"No LLM providers available"**
- Install and start Ollama, or set Deepseek API key

**"Command not found"**
- Ensure Python 3.8+ is installed
- Check PATH environment variable

**Slow responses**
- Use local Ollama instead of cloud APIs
- Reduce context size in configuration

**Permission errors**
- Run with appropriate user permissions
- Check file/directory access rights

### Debug Mode
```bash
python main.py --log-level DEBUG
```

### Get Help
```bash
python main.py --help
python main.py status
```

## 🎉 You're Ready!

Start exploring with natural language commands:
- "Help me organize these files"
- "Show me what changed in git"
- "Find all TODO comments in my code"
- "Create a backup of important files"

The AI will break down complex tasks and execute them step by step!

---

**Need more help?** Check the full README.md or run `/help` in the tool.
