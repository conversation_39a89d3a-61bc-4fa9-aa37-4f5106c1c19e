"""Utility helper functions."""

import os
import sys
import platform
import shutil
from pathlib import Path
from typing import Optional, Dict, Any, List
import subprocess
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


def detect_platform() -> str:
    """Detect the current platform.
    
    Returns:
        Platform string: 'windows', 'macos', 'linux', or 'unknown'
    """
    system = platform.system().lower()
    
    if system == "windows":
        return "windows"
    elif system == "darwin":
        return "macos"
    elif system == "linux":
        return "linux"
    else:
        return "unknown"


def is_wsl() -> bool:
    """Check if running in Windows Subsystem for Linux.
    
    Returns:
        True if running in WSL
    """
    try:
        with open('/proc/version', 'r') as f:
            return 'microsoft' in f.read().lower()
    except:
        return False


def find_executable(name: str) -> Optional[str]:
    """Find executable in PATH.
    
    Args:
        name: Executable name
        
    Returns:
        Full path to executable or None
    """
    return shutil.which(name)


def check_git_repository(path: Optional[Path] = None) -> bool:
    """Check if path is in a git repository.
    
    Args:
        path: Path to check (defaults to current directory)
        
    Returns:
        True if in git repository
    """
    if path is None:
        path = Path.cwd()
    
    try:
        result = subprocess.run(
            ["git", "rev-parse", "--git-dir"],
            cwd=path,
            capture_output=True,
            timeout=5
        )
        return result.returncode == 0
    except:
        return False


def get_file_type(file_path: Path) -> str:
    """Get file type based on extension.
    
    Args:
        file_path: Path to file
        
    Returns:
        File type string
    """
    suffix = file_path.suffix.lower()
    
    type_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.html': 'html',
        '.css': 'css',
        '.json': 'json',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.xml': 'xml',
        '.md': 'markdown',
        '.txt': 'text',
        '.sh': 'shell',
        '.bat': 'batch',
        '.ps1': 'powershell',
        '.sql': 'sql',
        '.go': 'go',
        '.rs': 'rust',
        '.cpp': 'cpp',
        '.c': 'c',
        '.java': 'java',
        '.php': 'php',
        '.rb': 'ruby'
    }
    
    return type_map.get(suffix, 'unknown')


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to maximum length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncated
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def safe_filename(filename: str) -> str:
    """Create a safe filename by removing/replacing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Safe filename
    """
    # Characters not allowed in filenames
    invalid_chars = '<>:"/\\|?*'
    
    safe_name = filename
    for char in invalid_chars:
        safe_name = safe_name.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    safe_name = safe_name.strip(' .')
    
    # Ensure it's not empty
    if not safe_name:
        safe_name = "unnamed"
    
    return safe_name


def parse_command_line(command: str) -> List[str]:
    """Parse command line into arguments, respecting quotes.
    
    Args:
        command: Command line string
        
    Returns:
        List of arguments
    """
    import shlex
    try:
        return shlex.split(command)
    except ValueError:
        # Fallback to simple split if shlex fails
        return command.split()


def get_terminal_size() -> tuple[int, int]:
    """Get terminal size.
    
    Returns:
        Tuple of (width, height)
    """
    try:
        size = shutil.get_terminal_size()
        return size.columns, size.lines
    except:
        return 80, 24  # Default fallback


def is_binary_file(file_path: Path) -> bool:
    """Check if file is binary.
    
    Args:
        file_path: Path to file
        
    Returns:
        True if file appears to be binary
    """
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(1024)
            
        # Check for null bytes (common in binary files)
        if b'\x00' in chunk:
            return True
        
        # Check for high ratio of non-printable characters
        printable_chars = sum(1 for byte in chunk if 32 <= byte <= 126 or byte in (9, 10, 13))
        ratio = printable_chars / len(chunk) if chunk else 1
        
        return ratio < 0.7
        
    except:
        return True  # Assume binary if we can't read it


def create_backup(file_path: Path, backup_dir: Optional[Path] = None) -> Path:
    """Create a backup of a file.
    
    Args:
        file_path: File to backup
        backup_dir: Directory for backup (defaults to same directory)
        
    Returns:
        Path to backup file
    """
    if backup_dir is None:
        backup_dir = file_path.parent
    
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate backup filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}.bak"
    backup_path = backup_dir / backup_name
    
    # Copy file
    shutil.copy2(file_path, backup_path)
    
    return backup_path


def validate_path(path_str: str, must_exist: bool = True) -> Optional[Path]:
    """Validate and resolve a path string.
    
    Args:
        path_str: Path string to validate
        must_exist: Whether path must exist
        
    Returns:
        Resolved Path object or None if invalid
    """
    try:
        path = Path(path_str).expanduser().resolve()
        
        if must_exist and not path.exists():
            return None
        
        return path
        
    except Exception:
        return None


def get_environment_info() -> Dict[str, Any]:
    """Get comprehensive environment information.
    
    Returns:
        Dictionary with environment details
    """
    return {
        "platform": detect_platform(),
        "is_wsl": is_wsl(),
        "python_version": platform.python_version(),
        "python_executable": sys.executable,
        "current_directory": str(Path.cwd()),
        "home_directory": str(Path.home()),
        "terminal_size": get_terminal_size(),
        "environment_variables": {
            "PATH": os.getenv("PATH", ""),
            "HOME": os.getenv("HOME", ""),
            "USER": os.getenv("USER", os.getenv("USERNAME", "")),
            "SHELL": os.getenv("SHELL", ""),
            "TERM": os.getenv("TERM", "")
        },
        "available_tools": {
            "git": find_executable("git") is not None,
            "curl": find_executable("curl") is not None,
            "wget": find_executable("wget") is not None,
            "docker": find_executable("docker") is not None,
            "node": find_executable("node") is not None,
            "npm": find_executable("npm") is not None,
            "python": find_executable("python") is not None,
            "pip": find_executable("pip") is not None
        }
    }


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    import signal
    
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        # The actual cleanup will be handled by the main application
        sys.exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # On Windows, also handle CTRL_BREAK_EVENT
    if platform.system() == "Windows":
        try:
            signal.signal(signal.SIGBREAK, signal_handler)
        except AttributeError:
            pass  # SIGBREAK not available on all Windows versions
