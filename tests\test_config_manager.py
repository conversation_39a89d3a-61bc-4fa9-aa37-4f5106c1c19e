"""Tests for ConfigManager."""

import pytest
import tempfile
import yaml
from pathlib import Path

from core.config_manager import ConfigManager


class TestConfigManager:
    """Test cases for ConfigManager."""
    
    def test_init_with_default_config(self):
        """Test initialization with default configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_manager = ConfigManager(str(config_path))
            
            assert config_manager.config_path == config_path
            assert isinstance(config_manager.config, dict)
            assert "llm" in config_manager.config
    
    def test_get_configuration_value(self):
        """Test getting configuration values."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_manager = ConfigManager(str(config_path))
            
            # Test nested key access
            value = config_manager.get("llm.default_provider")
            assert value is not None
            
            # Test default value
            value = config_manager.get("nonexistent.key", "default")
            assert value == "default"
    
    def test_set_configuration_value(self):
        """Test setting configuration values."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_manager = ConfigManager(str(config_path))
            
            # Set a value
            config_manager.set("test.key", "test_value")
            
            # Verify it was set
            assert config_manager.get("test.key") == "test_value"
    
    def test_api_key_retrieval(self):
        """Test API key retrieval from environment and config."""
        import os

        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_manager = ConfigManager(str(config_path))

            # Save original environment variable if it exists
            original_env_key = os.getenv("DEEPSEEK_API_KEY")

            try:
                # Clear environment variable for this test
                if "DEEPSEEK_API_KEY" in os.environ:
                    del os.environ["DEEPSEEK_API_KEY"]

                # Test with config file key only
                config_manager.set("llm.deepseek.api_key", "config_key")
                api_key = config_manager.get_api_key("deepseek")
                assert api_key == "config_key"

                # Test environment variable takes precedence
                os.environ["DEEPSEEK_API_KEY"] = "env_key"
                api_key = config_manager.get_api_key("deepseek")
                assert api_key == "env_key"

                # Test fallback to config when env is empty
                os.environ["DEEPSEEK_API_KEY"] = ""
                api_key = config_manager.get_api_key("deepseek")
                assert api_key == "config_key"

            finally:
                # Restore original environment variable
                if original_env_key is not None:
                    os.environ["DEEPSEEK_API_KEY"] = original_env_key
                elif "DEEPSEEK_API_KEY" in os.environ:
                    del os.environ["DEEPSEEK_API_KEY"]
    
    def test_save_and_load_config(self):
        """Test saving and loading configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            # Create and modify config
            config_manager = ConfigManager(str(config_path))
            config_manager.set("test.value", "saved_value")
            config_manager.save_config()
            
            # Create new instance and verify value persisted
            new_config_manager = ConfigManager(str(config_path))
            assert new_config_manager.get("test.value") == "saved_value"
    
    def test_storage_path_resolution(self):
        """Test storage path resolution."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_manager = ConfigManager(str(config_path))
            
            # Test path resolution
            storage_path = config_manager.get_storage_path("sessions.storage_path")
            assert isinstance(storage_path, Path)
            assert storage_path.exists()  # Should be created
