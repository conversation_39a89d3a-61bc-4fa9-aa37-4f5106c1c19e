"""Configuration management for the AI CLI tool."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages application configuration and settings."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file. If None, uses default locations.
        """
        self.config_path = self._resolve_config_path(config_path)
        self.config_dir = self.config_path.parent
        self.config: Dict[str, Any] = {}
        
        # Load environment variables
        load_dotenv()
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Load configuration
        self.load_config()
    
    def _resolve_config_path(self, config_path: Optional[str]) -> Path:
        """Resolve the configuration file path."""
        if config_path:
            return Path(config_path).expanduser().resolve()
        
        # Try multiple locations
        possible_paths = [
            Path.cwd() / "config.yaml",
            Path.home() / ".config" / "ai_cli_tool" / "config.yaml",
            Path.home() / ".ai_cli_tool" / "config.yaml",
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        # Default to user config directory
        return Path.home() / ".config" / "ai_cli_tool" / "config.yaml"
    
    def load_config(self) -> None:
        """Load configuration from file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                logger.info(f"Configuration loaded from {self.config_path}")
            else:
                # Create default configuration
                self.config = self._get_default_config()
                self.save_config()
                logger.info(f"Created default configuration at {self.config_path}")
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self.config = self._get_default_config()
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'llm.ollama.server_url')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'llm.ollama.server_url')
            value: Value to set
        """
        keys = key.split('.')
        config = self.config
        
        # Navigate to parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a provider, checking environment variables first.

        Args:
            provider: Provider name (e.g., 'deepseek')

        Returns:
            API key or None
        """
        # Check environment variable first
        env_var = f"{provider.upper()}_API_KEY"
        api_key = os.getenv(env_var)

        # Only use environment variable if it's not empty
        if api_key and api_key.strip():
            return api_key

        # Check configuration file
        return self.get(f"llm.{provider}.api_key")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            "llm": {
                "default_provider": "ollama",
                "ollama": {
                    "server_url": "http://localhost:11434",
                    "default_model": "llama2",
                    "timeout": 30
                },
                "deepseek": {
                    "api_key": "",
                    "model": "deepseek-chat",
                    "base_url": "https://api.deepseek.com",
                    "timeout": 30
                }
            },
            "tools": {
                "shell": {
                    "confirmation_required": True,
                    "allow_sudo": False,
                    "timeout": 60
                },
                "file_operations": {
                    "confirmation_for_destructive": True,
                    "max_file_size_mb": 10
                },
                "web_search": {
                    "default_results": 5,
                    "timeout": 15
                }
            },
            "context": {
                "auto_gather": True,
                "max_files": 20,
                "max_file_size_kb": 100,
                "exclude_patterns": [
                    "*.pyc", "__pycache__", ".git", 
                    "node_modules", "*.log"
                ],
                "include_git_status": True
            },
            "sessions": {
                "auto_save": True,
                "max_history_items": 100,
                "storage_path": "~/.ai_cli_tool/sessions"
            },
            "ui": {
                "show_animations": True,
                "animation_speed": 0.1,
                "show_status_bar": True,
                "syntax_highlighting": True,
                "diff_context_lines": 3
            },
            "logging": {
                "level": "INFO",
                "file_path": "~/.ai_cli_tool/logs/app.log",
                "max_file_size_mb": 10,
                "backup_count": 5
            },
            "security": {
                "command_confirmation": True,
                "restricted_commands": [
                    "rm -rf", "sudo rm", "format", "del /f /s /q"
                ]
            }
        }
    
    def get_storage_path(self, path_key: str) -> Path:
        """Get resolved storage path.
        
        Args:
            path_key: Configuration key for path
            
        Returns:
            Resolved Path object
        """
        path_str = self.get(path_key, "~/.ai_cli_tool")
        path = Path(path_str).expanduser().resolve()
        path.mkdir(parents=True, exist_ok=True)
        return path
